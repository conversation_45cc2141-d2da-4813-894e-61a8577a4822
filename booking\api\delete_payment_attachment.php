<?php
session_start();
require_once '../../dbconnect/_dbconnect.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get JSON input
$rawInput = file_get_contents('php://input');
$input = json_decode($rawInput, true);

// Validate required fields
if (!isset($input['attachment_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing attachment ID']);
    exit;
}

$attachmentId = intval($input['attachment_id']);

try {
    // Get database connection
    $pdo = db_connect();
    
    // Get attachment details before deletion
    $stmt = $pdo->prepare("SELECT reference_image FROM kp_payment_references WHERE id = ?");
    $stmt->execute([$attachmentId]);
    $attachment = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$attachment) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Attachment not found']);
        exit;
    }
    
    // Delete the attachment record from database
    $stmt = $pdo->prepare("DELETE FROM kp_payment_references WHERE id = ?");
    $result = $stmt->execute([$attachmentId]);
    
    if ($result) {
        // Try to delete the physical file
        if ($attachment['reference_image']) {
            $filePath = '../../' . $attachment['reference_image'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Attachment deleted successfully'
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to delete attachment']);
    }
    
} catch (PDOException $e) {
    error_log("Database error in delete_payment_attachment.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
} catch (Exception $e) {
    error_log("General error in delete_payment_attachment.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'An error occurred']);
}
?>
