<?php
session_start();
if(!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true){
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Set error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

include '../dbconnect/_dbconnect.php';

try {
    // Get the date parameter
    $date = $_GET['date'] ?? date('d-m-Y');
    $searchTerm = $_GET['search'] ?? '';

    // Convert date format from dd-mm-yyyy to yyyy-mm-dd for database query
    $dateParts = explode('-', $date);
    if (count($dateParts) === 3) {
        $dbDate = $dateParts[2] . '-' . $dateParts[1] . '-' . $dateParts[0];
    } else {
        $dbDate = date('Y-m-d');
    }



    // Get bookings from database
    $conn = db_connect();
    $sql = "SELECT * FROM kp_booking WHERE use_date = :date";
    $params = [':date' => $dbDate];

    // Add search filter if provided
    if (!empty($searchTerm)) {
        $sql .= " AND (name LIKE :search OR phone LIKE :search OR orderNo LIKE :search)";
        $params[':search'] = '%' . $searchTerm . '%';
    }

    $sql .= " ORDER BY booking_id ASC";

    $stmt = $conn->prepare($sql);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();

    $bookings = $stmt->fetchAll(PDO::FETCH_ASSOC);


    
    // Set headers for Excel download
    $filename = "Bookings_" . str_replace('-', '_', $date);
    if (!empty($searchTerm)) {
        $filename .= "_filtered";
    }
    $filename .= ".xls";

    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    
    // Start output buffering
    ob_start();
    
    // Create Excel content
    echo '<?xml version="1.0"?>';
    echo '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">';
    echo '<Styles>';
    echo '<Style ss:ID="header">';
    echo '<Font ss:Bold="1"/>';
    echo '<Interior ss:Color="#CCE5FF" ss:Pattern="Solid"/>';
    echo '<Borders>';
    echo '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>';
    echo '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>';
    echo '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>';
    echo '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>';
    echo '</Borders>';
    echo '</Style>';
    echo '<Style ss:ID="data">';
    echo '<Borders>';
    echo '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>';
    echo '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>';
    echo '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>';
    echo '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>';
    echo '</Borders>';
    echo '</Style>';
    echo '</Styles>';
    $worksheetName = "Bookings_" . str_replace('-', '_', $date);
    if (!empty($searchTerm)) {
        $worksheetName .= "_filtered";
    }
    echo '<Worksheet ss:Name="' . $worksheetName . '">';
    echo '<Table>';
    
    // Add info row if filtered
    if (!empty($searchTerm)) {
        echo '<Row>';
        echo '<Cell ss:StyleID="header" ss:MergeAcross="18"><Data ss:Type="String">Filtered by: "' . htmlspecialchars($searchTerm) . '" | Total Records: ' . count($bookings) . '</Data></Cell>';
        echo '</Row>';
    }

    // Header row
    echo '<Row>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">Tables</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">Order No.</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">Customer Name</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">Phone</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">Agent</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">Voucher No.</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">Adults</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">Children</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">Infants</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">Guide</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">FOC</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">T/L</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">Floor</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">Book Status</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">Amount</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">Payment</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">Pay Type</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">Payment Note</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">Special Request</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">Remark</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">Use Date</Data></Cell>';
    echo '<Cell ss:StyleID="header"><Data ss:Type="String">Created At</Data></Cell>';
    echo '</Row>';
    
    // Data rows
    foreach ($bookings as $booking) {
        $specialRequest = '';
        if ($booking['special_request'] == 1) {
            $specialRequest = 'Birthday';
        } elseif ($booking['special_request'] == 2) {
            $specialRequest = 'Anniversary';
        } elseif ($booking['special_request'] == 3) {
            $specialRequest = 'Wedding';
        }

        // change date time to bangkok timezone
        $createDate = new DateTime($booking['create_date']);
        $createDate->modify('+7 hours');
        $createDate = $createDate->format('d-m-Y H:i:s');

        // Check if this booking has "Change" status and extract change note
        $changeNote = '';
        if ($booking['book_status'] === 'Change') {
            try {
                // First check if there's already a note in the remark field
                $remark = $booking['remark'] ?? '';
                if (strpos($remark, 'Moved to:') !== false) {
                    // Extract the existing note from the remark
                    if (preg_match('/Moved to: ([^|]+)/', $remark, $matches)) {
                        $changeNote = trim($matches[1]);
                    }
                } else {
                    // Fallback: Find the new booking created from this original booking
                    $findNewBookingSql = "SELECT use_date, orderNo FROM kp_booking
                                         WHERE name = :name
                                         AND phone = :phone
                                         AND agent = :agent
                                         AND create_date > :original_create_date
                                         AND book_status != 'Change'
                                         AND book_status != 'Cancel'
                                         ORDER BY create_date ASC
                                         LIMIT 1";

                    $findStmt = $conn->prepare($findNewBookingSql);
                    $findStmt->execute([
                        ':name' => $booking['name'],
                        ':phone' => $booking['phone'],
                        ':agent' => $booking['agent'],
                        ':original_create_date' => $booking['create_date']
                    ]);

                    $newBooking = $findStmt->fetch(PDO::FETCH_ASSOC);

                    if ($newBooking) {
                        $newDate = new DateTime($newBooking['use_date']);
                        $formattedNewDate = $newDate->format('d-m-Y');
                        $changeNote = "Changed to: {$formattedNewDate} (Order: {$newBooking['orderNo']})";
                    } else {
                        $changeNote = "Changed (new booking not found)";
                    }
                }
            } catch (Exception $e) {
                $changeNote = "Changed (error finding new date)";
                error_log("Error finding new booking date: " . $e->getMessage());
            }
        }

        echo '<Row>';
        echo '<Cell ss:StyleID="data"><Data ss:Type="String">' . htmlspecialchars($booking['tables'] ?? '') . '</Data></Cell>';
        echo '<Cell ss:StyleID="data"><Data ss:Type="String">' . htmlspecialchars($booking['orderNo'] ?? '') . '</Data></Cell>';
        echo '<Cell ss:StyleID="data"><Data ss:Type="String">' . htmlspecialchars($booking['name'] ?? '') . '</Data></Cell>';
        echo '<Cell ss:StyleID="data"><Data ss:Type="String">' . htmlspecialchars($booking['phone'] ?? '') . '</Data></Cell>';
        echo '<Cell ss:StyleID="data"><Data ss:Type="String">' . htmlspecialchars($booking['agent'] ?? '') . '</Data></Cell>';
        echo '<Cell ss:StyleID="data"><Data ss:Type="String">' . htmlspecialchars($booking['voucher'] ?? '') . '</Data></Cell>';
        echo '<Cell ss:StyleID="data"><Data ss:Type="Number">' . htmlspecialchars($booking['adult'] ?? '0') . '</Data></Cell>';
        echo '<Cell ss:StyleID="data"><Data ss:Type="Number">' . htmlspecialchars($booking['child'] ?? '0') . '</Data></Cell>';
        echo '<Cell ss:StyleID="data"><Data ss:Type="Number">' . htmlspecialchars($booking['infant'] ?? '0') . '</Data></Cell>';
        echo '<Cell ss:StyleID="data"><Data ss:Type="Number">' . htmlspecialchars($booking['guide'] ?? '0') . '</Data></Cell>';
        echo '<Cell ss:StyleID="data"><Data ss:Type="Number">' . htmlspecialchars($booking['inspection'] ?? '0') . '</Data></Cell>';
        echo '<Cell ss:StyleID="data"><Data ss:Type="Number">' . htmlspecialchars($booking['team_leader'] ?? '0') . '</Data></Cell>';
        echo '<Cell ss:StyleID="data"><Data ss:Type="String">' . htmlspecialchars($booking['use_zone'] ?? '') . '</Data></Cell>';
        echo '<Cell ss:StyleID="data"><Data ss:Type="String">' . htmlspecialchars($booking['book_status'] ?? '') . '</Data></Cell>';
        echo '<Cell ss:StyleID="data"><Data ss:Type="Number">' . htmlspecialchars($booking['amount'] ?? '0') . '</Data></Cell>';
        echo '<Cell ss:StyleID="data"><Data ss:Type="String">' . htmlspecialchars($booking['payment_status'] ?? '') . '</Data></Cell>';
        echo '<Cell ss:StyleID="data"><Data ss:Type="String">' . htmlspecialchars($booking['pay_type'] ?? '') . '</Data></Cell>';
        echo '<Cell ss:StyleID="data"><Data ss:Type="String">' . htmlspecialchars($booking['payment_note'] ?? '') . '</Data></Cell>';
        echo '<Cell ss:StyleID="data"><Data ss:Type="String">' . htmlspecialchars($specialRequest ?? '') . '</Data></Cell>';

        // Combine original remark with change note
        $remarkText = $booking['remark'] ?? '';
        if (!empty($changeNote)) {
            $remarkText = !empty($remarkText) ? $remarkText . ' | ' . $changeNote : $changeNote;
        }
        echo '<Cell ss:StyleID="data"><Data ss:Type="String">' . htmlspecialchars($remarkText) . '</Data></Cell>';

        echo '<Cell ss:StyleID="data"><Data ss:Type="String">' . htmlspecialchars($booking['use_date'] ?? '') . '</Data></Cell>';
        echo '<Cell ss:StyleID="data"><Data ss:Type="String">' . htmlspecialchars($createDate ?? '') . '</Data></Cell>';
        echo '</Row>';
    }
    
    echo '</Table>';
    echo '</Worksheet>';
    echo '</Workbook>';
    
    // Get the content and clean the buffer
    $content = ob_get_clean();
    
    // Output the content
    echo $content;
    
} catch (Exception $e) {
    // Clear any output buffer
    if (ob_get_level()) {
        ob_end_clean();
    }
    
    // Set JSON header for error response
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>
