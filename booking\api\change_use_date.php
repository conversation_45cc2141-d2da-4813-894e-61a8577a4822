<?php
session_start();
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Include database connection
include '../../dbconnect/_dbconnect.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Get POST data
        $bookingId = isset($_POST['booking_id']) ? (int)$_POST['booking_id'] : 0;
        $newUseDate = isset($_POST['new_use_date']) ? $_POST['new_use_date'] : '';
        $adults = isset($_POST['adults']) ? (int)$_POST['adults'] : null;
        $children = isset($_POST['children']) ? (int)$_POST['children'] : null;
        $infants = isset($_POST['infants']) ? (int)$_POST['infants'] : null;
        $newTables = isset($_POST['new_tables']) ? $_POST['new_tables'] : null;

        // Validate input
        if ($bookingId <= 0) {
            throw new Exception('Invalid booking ID');
        }

        if (empty($newUseDate)) {
            throw new Exception('New use date is required');
        }

        // Validate date format
        $dateTime = DateTime::createFromFormat('Y-m-d', $newUseDate);
        if (!$dateTime || $dateTime->format('Y-m-d') !== $newUseDate) {
            throw new Exception('Invalid date format');
        }

        // Connect to database
        $conn = db_connect();

        // Add book_status column if it doesn't exist
        $checkColumnSql = "SELECT COUNT(*) as count FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'kp_booking' AND column_name = 'book_status'";
        $checkColumnStmt = $conn->prepare($checkColumnSql);
        $checkColumnStmt->execute();
        $columnExists = $checkColumnStmt->fetch(PDO::FETCH_ASSOC)['count'] > 0;

        if (!$columnExists) {
            $conn->exec("ALTER TABLE kp_booking ADD COLUMN book_status VARCHAR(20) DEFAULT 'Pending' COMMENT 'Booking status: Pending, Confirmed, Complete, Cancel, Change'");
        }

        // Get all data from the original booking
        $checkSql = "SELECT * FROM kp_booking WHERE booking_id = :booking_id";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->bindParam(':booking_id', $bookingId, PDO::PARAM_INT);
        $checkStmt->execute();

        $originalBooking = $checkStmt->fetch(PDO::FETCH_ASSOC);
        if (!$originalBooking) {
            throw new Exception('Booking not found');
        }

        // Generate new order number for the cloned booking
        $newOrderNo = 'SWD' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);

        // Ensure the new order number is unique
        $checkOrderSql = "SELECT COUNT(*) as count FROM kp_booking WHERE orderNo = :orderNo";
        $checkOrderStmt = $conn->prepare($checkOrderSql);

        do {
            $checkOrderStmt->execute([':orderNo' => $newOrderNo]);
            $orderExists = $checkOrderStmt->fetch(PDO::FETCH_ASSOC)['count'] > 0;

            if ($orderExists) {
                $newOrderNo = 'SWD' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            }
        } while ($orderExists);

        // Start transaction
        $conn->beginTransaction();

        // Prepare notes for both bookings
        $originalDate = new DateTime($originalBooking['use_date']);
        $newDate = new DateTime($newUseDate);
        $formattedOriginalDate = $originalDate->format('d-m-Y');
        $formattedNewDate = $newDate->format('d-m-Y');

        // Note for new booking: moved from original date and booking ID
        $newBookingNote = "Moved from: {$formattedOriginalDate} (Original Booking ID: {$bookingId})";
        $newBookingRemark = $originalBooking['remark'] ?? '';
        if (!empty($newBookingRemark)) {
            $newBookingRemark .= ' | ' . $newBookingNote;
        } else {
            $newBookingRemark = $newBookingNote;
        }

        // Clone the booking with new data
        $cloneSql = "INSERT INTO kp_booking (
            orderNo, name, phone, adult, child, infant, guide, inspection, team_leader,
            use_date, use_zone, voucher, agent, remark, special_request, cruise_id, zone_id,
            tables, amount, user_key, payment_status, payment_note, attactfile, pay_type,
            credit_term, book_status, create_date, update_date
        ) VALUES (
            :orderNo, :name, :phone, :adult, :child, :infant, :guide, :inspection, :team_leader,
            :use_date, :use_zone, :voucher, :agent, :remark, :special_request, :cruise_id, :zone_id,
            :tables, :amount, :user_key, :payment_status, :payment_note, :attactfile, :pay_type,
            :credit_term, :book_status, NOW(), NOW()
        )";

        $cloneStmt = $conn->prepare($cloneSql);
        $cloneResult = $cloneStmt->execute([
            ':orderNo' => $newOrderNo,
            ':name' => $originalBooking['name'],
            ':phone' => $originalBooking['phone'],
            ':adult' => $adults ?? $originalBooking['adult'],
            ':child' => $children ?? $originalBooking['child'],
            ':infant' => $infants ?? $originalBooking['infant'],
            ':guide' => $originalBooking['guide'],
            ':inspection' => $originalBooking['inspection'],
            ':team_leader' => $originalBooking['team_leader'],
            ':use_date' => $newUseDate,
            ':use_zone' => $originalBooking['use_zone'],
            ':voucher' => $originalBooking['voucher'],
            ':agent' => $originalBooking['agent'],
            ':remark' => $newBookingRemark,
            ':special_request' => $originalBooking['special_request'],
            ':cruise_id' => $originalBooking['cruise_id'],
            ':zone_id' => $originalBooking['zone_id'],
            ':tables' => $newTables ?? $originalBooking['tables'],
            ':amount' => $originalBooking['amount'],
            ':user_key' => $originalBooking['user_key'],
            ':payment_status' => $originalBooking['payment_status'] ?? 'WP',
            ':payment_note' => $originalBooking['payment_note'] ?? null,
            ':attactfile' => $originalBooking['attactfile'] ?? null,
            ':pay_type' => $originalBooking['pay_type'] ?? 'Transfer',
            ':credit_term' => $originalBooking['credit_term'] ?? null,
            ':book_status' => 'Pending'
        ]);

        if (!$cloneResult) {
            $conn->rollBack();
            throw new Exception('Failed to create new booking');
        }

        $newBookingId = $conn->lastInsertId();

        // Note for original booking: moved to new date and new booking ID
        $originalBookingNote = "Moved to: {$formattedNewDate} (New Booking ID: {$newBookingId})";
        $originalBookingRemark = $originalBooking['remark'] ?? '';
        if (!empty($originalBookingRemark)) {
            $originalBookingRemark .= ' | ' . $originalBookingNote;
        } else {
            $originalBookingRemark = $originalBookingNote;
        }

        // Update the original booking: set Adults, Children, Infants to 0, clear tables, set amount to 0, set book_status to 'Change', and add note
        $updateOriginalSql = "UPDATE kp_booking SET
            adult = 0,
            child = 0,
            infant = 0,
            tables = '',
            amount = 0,
            book_status = 'Change',
            remark = :remark,
            update_date = NOW()
            WHERE booking_id = :booking_id";

        $updateOriginalStmt = $conn->prepare($updateOriginalSql);
        $updateOriginalResult = $updateOriginalStmt->execute([
            ':booking_id' => $bookingId,
            ':remark' => $originalBookingRemark
        ]);

        if (!$updateOriginalResult) {
            $conn->rollBack();
            throw new Exception('Failed to update original booking');
        }

        // Copy payment reference images to the new booking if they exist
        try {
            $copyReferencesSql = "INSERT INTO kp_payment_references (booking_id, payment_status, reference_image, note, updated_by, created_at)
                                  SELECT :new_booking_id, payment_status, reference_image, note, updated_by, created_at
                                  FROM kp_payment_references
                                  WHERE booking_id = :original_booking_id";

            $copyReferencesStmt = $conn->prepare($copyReferencesSql);
            $copyReferencesStmt->execute([
                ':new_booking_id' => $newBookingId,
                ':original_booking_id' => $bookingId
            ]);
        } catch (Exception $e) {
            // If payment references table doesn't exist, continue without error
            error_log("Payment references table not found or error copying: " . $e->getMessage());
        }

        // Commit the transaction
        $conn->commit();

        echo json_encode([
            'success' => true,
            'message' => 'Booking date changed successfully. New booking created with move note and original booking updated with change note.',
            'data' => [
                'original_booking_id' => $bookingId,
                'new_booking_id' => $newBookingId,
                'new_order_no' => $newOrderNo,
                'old_date' => $originalBooking['use_date'],
                'new_date' => $newUseDate,
                'original_status' => 'Change',
                'new_status' => 'Pending',
                'original_note' => $originalBookingNote,
                'new_note' => $newBookingNote,
                'formatted_old_date' => $formattedOriginalDate,
                'formatted_new_date' => $formattedNewDate
            ]
        ]);

    } catch (Exception $e) {
        // Rollback transaction if it was started
        if ($conn && $conn->inTransaction()) {
            $conn->rollBack();
        }

        error_log("Change use date error: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method'
    ]);
}
?>
