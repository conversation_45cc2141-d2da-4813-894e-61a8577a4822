-- Add book_status column to kp_booking table if it doesn't exist
-- This script is safe to run multiple times

-- Check and add book_status column
SET @exists_book_status = 0;
SELECT COUNT(*) INTO @exists_book_status
FROM information_schema.columns
WHERE table_schema = DATABASE()
    AND table_name = 'kp_booking'
    AND column_name = 'book_status';

SET @sql_book_status = IF(
        @exists_book_status = 0,
        'ALTER TABLE kp_booking ADD COLUMN book_status VARCHAR(20) DEFAULT "Pending" COMMENT "Booking status: Pending, Confirmed, Complete, Cancel, Change"',
        'SELECT "Column book_status already exists" as message'
    );

PREPARE stmt
FROM @sql_book_status;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update existing records to have default booking status if they are NULL
UPDATE kp_booking
SET book_status = 'Pending'
WHERE book_status IS NULL
    OR book_status = '';

-- Show the result
SELECT CASE
        WHEN @exists_book_status = 0 THEN 'book_status column added'
        ELSE 'book_status column exists'
    END as book_status_result;
