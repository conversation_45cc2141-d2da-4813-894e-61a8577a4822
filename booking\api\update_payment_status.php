<?php
session_start();
require_once '../../dbconnect/_dbconnect.php';
require_once '../utils/BookingLogger.php';

// Array
// (
//     [booking_id] => 212
//     [payment_status] => Paid
//     [note] => Anuwat
// )


// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Handle both JSON and FormData requests
$input = [];
$uploadedFiles = [];

if (isset($_POST['booking_id'])) {
    // FormData request (with file upload)
    $input = $_POST;

    // Handle multiple files
    if (isset($_FILES['payment_references']) && is_array($_FILES['payment_references']['name'])) {
        // Multiple files uploaded
        for ($i = 0; $i < count($_FILES['payment_references']['name']); $i++) {
            if ($_FILES['payment_references']['error'][$i] === UPLOAD_ERR_OK) {
                $uploadedFiles[] = [
                    'name' => $_FILES['payment_references']['name'][$i],
                    'type' => $_FILES['payment_references']['type'][$i],
                    'tmp_name' => $_FILES['payment_references']['tmp_name'][$i],
                    'error' => $_FILES['payment_references']['error'][$i],
                    'size' => $_FILES['payment_references']['size'][$i]
                ];
            }
        }
    } elseif (isset($_FILES['payment_reference'])) {
        // Single file (backward compatibility)
        if ($_FILES['payment_reference']['error'] === UPLOAD_ERR_OK) {
            $uploadedFiles[] = $_FILES['payment_reference'];
        }
    }

    error_log("Payment status update request - FormData: " . json_encode($_POST));
    error_log("Uploaded files count: " . count($uploadedFiles));
} else {
    // JSON request (backward compatibility)
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);
    error_log("Payment status update request - JSON: " . $rawInput);
}

// Validate required fields
if (!isset($input['booking_id']) || !isset($input['payment_status'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing required fields', 'received' => $input]);
    exit;
}

$bookingId = intval($input['booking_id']);
$paymentStatus = trim($input['payment_status']);
$note = isset($input['note']) ? trim($input['note']) : '';
$referenceImages = [];

// Validate payment status
$allowedStatuses = ['WP', 'Paid'];
if (!in_array($paymentStatus, $allowedStatuses)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid payment status']);
    exit;
}

// Handle multiple file uploads if present
if (!empty($uploadedFiles)) {
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    $maxSize = 5 * 1024 * 1024; // 5MB
    $maxFiles = 5; // Maximum 5 files

    if (count($uploadedFiles) > $maxFiles) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Too many files. Maximum 5 images allowed.']);
        exit;
    }

    // Create upload directory if it doesn't exist
    $uploadDir = '../../uploads/payment_references/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    foreach ($uploadedFiles as $index => $uploadedFile) {
        // Validate file type
        if (!in_array($uploadedFile['type'], $allowedTypes)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => "Invalid file type for file '{$uploadedFile['name']}'. Only JPG, PNG, and GIF are allowed."]);
            exit;
        }

        // Validate file size
        if ($uploadedFile['size'] > $maxSize) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => "File '{$uploadedFile['name']}' is too large. Maximum size is 5MB."]);
            exit;
        }

        // Generate unique filename
        $fileExtension = pathinfo($uploadedFile['name'], PATHINFO_EXTENSION);
        $fileName = 'payment_' . $bookingId . '_' . time() . '_' . $index . '.' . $fileExtension;
        $filePath = $uploadDir . $fileName;

        // Move uploaded file
        if (move_uploaded_file($uploadedFile['tmp_name'], $filePath)) {
            $referenceImages[] = '../uploads/payment_references/' . $fileName;
            error_log("Payment reference image uploaded: " . $fileName);
        } else {
            error_log("Failed to move uploaded file: " . $uploadedFile['name']);
        }
    }
}

try {
    // Get database connection
    $pdo = db_connect();

    // Check and add required columns if they don't exist
    $columnsToCheck = [
        'payment_status' => "VARCHAR(10) DEFAULT 'WP' COMMENT 'Payment status: WP=Waiting Payment, Paid=Paid'",
        'payment_note' => "TEXT DEFAULT NULL COMMENT 'Payment note or comment'",
        'attactfile' => "VARCHAR(255) DEFAULT NULL COMMENT 'Attachment file path'"
    ];

    foreach ($columnsToCheck as $columnName => $columnDefinition) {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'kp_booking' AND column_name = ?");
        $stmt->execute([$columnName]);
        $columnExists = $stmt->fetch(PDO::FETCH_ASSOC)['count'] > 0;

        if (!$columnExists) {
            $pdo->exec("ALTER TABLE kp_booking ADD COLUMN {$columnName} {$columnDefinition}");
            error_log("Added {$columnName} column to kp_booking table");
        }
    }

    // Create payment references table if it doesn't exist
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS `kp_payment_references` (
            `id` INT NOT NULL AUTO_INCREMENT,
            `booking_id` INT NOT NULL,
            `payment_status` VARCHAR(10) NOT NULL,
            `reference_image` VARCHAR(255) DEFAULT NULL,
            `note` TEXT DEFAULT NULL,
            `updated_by` INT DEFAULT NULL,
            `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `booking_id` (`booking_id`),
            KEY `created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ";
    $pdo->exec($createTableSQL);

    // Check if booking exists and get current payment status
    $stmt = $pdo->prepare("SELECT booking_id, orderNo, name, payment_status FROM kp_booking WHERE booking_id = ?");
    $stmt->execute([$bookingId]);
    $booking = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$booking) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Booking not found']);
        exit;
    }

    // Store original payment status for logging
    $originalPaymentStatus = $booking['payment_status'] ?? 'WP';

    // Update payment status, note, and attachment file
    $updateFields = ['payment_status = ?'];
    $updateValues = [$paymentStatus];

    // If status is "Cancel", clear tables, adult, child, and infant fields
    if ($paymentStatus === 'Cancel') {
        $updateFields[] = 'tables = ?';
        $updateFields[] = 'adult = ?';
        $updateFields[] = 'child = ?';
        $updateFields[] = 'infant = ?';
        $updateValues[] = ''; // Clear tables
        $updateValues[] = 0;  // Clear adult
        $updateValues[] = 0;  // Clear child
        $updateValues[] = 0;  // Clear infant
    }

    if ($note) {
        $updateFields[] = 'payment_note = ?';
        $updateValues[] = $note;
    }

    // For backward compatibility, still update attactfile with the first image if any
    if (!empty($referenceImages)) {
        $updateFields[] = 'attactfile = ?';
        $updateValues[] = $referenceImages[0];
    }

    $updateValues[] = $bookingId; // for WHERE clause

    $sql = "UPDATE kp_booking SET " . implode(', ', $updateFields) . " WHERE booking_id = ?";
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute($updateValues);

    // Insert new payment reference images into the separate table
    if ($result && !empty($referenceImages)) {
        $userId = $_SESSION['user_id'] ?? null;

        foreach ($referenceImages as $imagePath) {
            $stmt = $pdo->prepare("INSERT INTO kp_payment_references (booking_id, payment_status, reference_image, note, updated_by) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$bookingId, $paymentStatus, $imagePath, $note, $userId]);
        }
    }
    
    if ($result) {
        // --- Log the payment status change ---
        try {
            $logger = new BookingLogger($pdo);

            $logNotes = "Payment status changed from '{$originalPaymentStatus}' to '{$paymentStatus}'";
            if ($note) {
                $logNotes .= ". Note: {$note}";
            }
            if ($referenceImage) {
                $logNotes .= ". Reference image uploaded: {$referenceImage}";
            }

            $logger->logPaymentStatusChange(
                $bookingId,
                $booking['orderNo'],
                $originalPaymentStatus,
                $paymentStatus,
                $logNotes
            );

        } catch (Exception $e) {
            error_log("Failed to log payment status change: " . $e->getMessage());
            // Don't fail the update if logging fails
        }

        $response = [
            'success' => true,
            'message' => 'Payment status updated successfully',
            'booking_id' => $bookingId,
            'payment_status' => $paymentStatus,
            'reference_image' => $referenceImage,
            'note' => $note
        ];

        // If status is "Cancel", include the updated booking data
        if ($paymentStatus === 'Cancel') {
            $response['updated_booking'] = [
                'tables' => '',
                'adult' => 0,
                'child' => 0,
                'infant' => 0
            ];
        }

        echo json_encode($response);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to update payment status']);
    }
    
} catch (Exception $e) {
    error_log("Error updating payment status: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    http_response_code(500);

    // For debugging, include the actual error message
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred: ' . $e->getMessage(),
        'debug' => [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
