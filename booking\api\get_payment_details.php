<?php
session_start();
require_once '../../dbconnect/_dbconnect.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get booking ID from query parameter
$bookingId = isset($_GET['booking_id']) ? intval($_GET['booking_id']) : 0;

if (!$bookingId) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing booking ID']);
    exit;
}

try {
    // Get database connection
    $pdo = db_connect();

    // Get payment details for the booking
    $stmt = $pdo->prepare("SELECT payment_status, payment_note, attactfile FROM kp_booking WHERE booking_id = ?");
    $stmt->execute([$bookingId]);
    $paymentDetails = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$paymentDetails) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Booking not found']);
        exit;
    }

    // Get payment reference attachments from the separate table
    $attachments = [];
    try {
        $stmt = $pdo->prepare("SELECT id, reference_image, note, created_at FROM kp_payment_references WHERE booking_id = ? ORDER BY created_at DESC");
        $stmt->execute([$bookingId]);
        $attachments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // If table doesn't exist yet, continue with empty attachments
        error_log("Payment references table not found: " . $e->getMessage());
    }

    // If no attachments in the new table but there's an old attactfile, include it
    if (empty($attachments) && !empty($paymentDetails['attactfile'])) {
        $attachments[] = [
            'id' => 0, // Legacy attachment, no ID
            'reference_image' => $paymentDetails['attactfile'],
            'note' => '',
            'created_at' => date('Y-m-d H:i:s') // Use current time as fallback
        ];
    }

    echo json_encode([
        'success' => true,
        'payment_status' => $paymentDetails['payment_status'] ?? 'WP',
        'payment_note' => $paymentDetails['payment_note'] ?? '',
        'attactfile' => $paymentDetails['attactfile'] ?? '', // Keep for backward compatibility
        'attachments' => $attachments
    ]);
    
} catch (Exception $e) {
    error_log("Error getting payment details: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
}
?>
