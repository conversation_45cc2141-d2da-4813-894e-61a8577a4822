
<?php

// print_r($_POST);
// {"success":true,"message":"Booking status updated successfully","booking_id":216,"booking_status":"Cancel","booking_note":"abcde"}

session_start();
require_once '../../dbconnect/_dbconnect.php';
require_once '../utils/BookingLogger.php';

header('Content-Type: application/json');

if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Sanitize and validate inputs
$bookingId = intval($_POST['booking_id']);
$bookingStatus = trim($_POST['booking_status']);
$bookingNote = isset($_POST['booking_note']) ? trim($_POST['booking_note']) : '';

// Validate required fields
if (empty($bookingId) || empty($bookingStatus) || empty($bookingNote)) {
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit;
}

// Validate booking status
$allowedStatuses = ['Pending', 'Complete', 'Cancel', 'Change'];
if (!in_array($bookingStatus, $allowedStatuses)) {
    echo json_encode(['success' => false, 'message' => 'Invalid booking status']);
    exit;
}

try {
    // Connect to the database
    $pdo = db_connect();

    // Get the original booking status
    $stmt = $pdo->prepare("SELECT book_status, orderNo FROM kp_booking WHERE booking_id = ?");
    $stmt->execute([$bookingId]);
    $originalBooking = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$originalBooking) {
        echo json_encode(['success' => false, 'message' => 'Booking not found']);
        exit;
    }

    // Update the booking status
    $stmt = $pdo->prepare("UPDATE kp_booking SET book_status = ?, bookChangeStatus = ? WHERE booking_id = ?");
    $stmt->execute([$bookingStatus, $bookingNote, $bookingId]);

    if ($bookingStatus === 'Cancel') {
        $stmt = $pdo->prepare("UPDATE kp_booking SET tables = '', adult = 0, child = 0, infant = 0, amount = 0 WHERE booking_id = ?");
        $stmt->execute([$bookingId]);
    }

    // --- Log the booking status change ---
    try {
        $logger = new BookingLogger($pdo);

        $logNotes = "Booking status changed from '{$originalBooking['book_status']}' to '{$bookingStatus}'";
        if ($bookingNote) {
            $logNotes .= ". Note: {$bookingNote}";
        }

        $logger->logBookingEdit(
            $bookingId,
            $originalBooking['orderNo'] ?? '',
            ['book_status' => $originalBooking['book_status']],
            ['book_status' => $bookingStatus],
            'STATUS_CHANGE',
            $logNotes
        );

    } catch (Exception $e) {
        error_log("Failed to log booking status change: " . $e->getMessage());
        // Don't fail the update if logging fails
    }

    echo json_encode([
        'success' => true,
        'message' => 'Booking status updated successfully',
        'booking_id' => $bookingId,
        'booking_status' => $bookingStatus,
        'booking_note' => $bookingNote
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Failed to update booking status']);
}?>
