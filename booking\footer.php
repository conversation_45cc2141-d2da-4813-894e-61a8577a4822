
  <script src="../assets/js/vendor.min.js"></script>
  <!-- Import Js Files -->
  <script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
  <script src="../assets/libs/simplebar/dist/simplebar.min.js"></script>
  <script src="../assets/js/theme/app.init.js"></script>
  <script src="../assets/js/theme/theme.js"></script>
  <script src="../assets/js/theme/app.min.js"></script>
  <script src="../assets/js/theme/sidebarmenu.js"></script>

  <!-- solar icons -->
  <script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>
  <!-- <script src="../assets/libs/fullcalendar/index.global.min.js"></script> -->
  <script src="../assets/js/apps/contact.js?v=1.0.2"></script>
  <!-- <script src="../assets/libs/prismjs/prism.js"></script> -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script> <!-- Link to SweetAlert2 -->

  <style>
  /* Disabled button styles for Change and Cancel status */
  .booking-status-btn:disabled,
  .change-date-btn:disabled {
      opacity: 0.6 !important;
      cursor: not-allowed !important;
      pointer-events: none;
  }

  .booking-status-btn:disabled:hover,
  .change-date-btn:disabled:hover {
      opacity: 0.6 !important;
  }
  </style>

  <script>
  function formatAndDisplayDate() {
    var input = document.getElementById('myDate');
    if (input.value) {
        var date = new Date(input.value);

        // Full date format: "Saturday, March 15, 2025"
        var fullOptions = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
        var fullFormattedDate = new Intl.DateTimeFormat('en-US', fullOptions).format(date);
        document.getElementById('fullDate').innerText = fullFormattedDate;

        // Short date format: "14-03-2025"
        var shortOptions = { year: 'numeric', month: '2-digit', day: '2-digit' };
        var shortFormattedDate = new Intl.DateTimeFormat('en-GB', shortOptions).format(date).replace(/\//g, '-');
        document.getElementById('shortDate').innerText = shortFormattedDate;

        console.log('Formatted date:', shortFormattedDate);

        // Fetch and display bookings by date
        fetchBookingsByDate(shortFormattedDate);
        getZoneJson(shortFormattedDate);
    } else {
        // If no date is selected, use today's date
        var today = new Date();

        // Set the date input value to today
        var year = today.getFullYear();
        var month = (today.getMonth() + 1).toString().padStart(2, '0');
        var day = today.getDate().toString().padStart(2, '0');
        input.value = year + '-' + month + '-' + day;

        // Call the function again to format and display the date
        formatAndDisplayDate();
    }
  }


  function fetchBookingsByDate(date) {
    console.log('Fetching bookings for date:', date);

    // Show loading indicator
    document.getElementById('output').innerHTML = '<tr><td colspan="11" class="text-center">Loading bookings...</td></tr>';

    // Fetch bookings timestamped with the selected date
    fetch('../api/getAPIbyMonth.php?date=' + date + '&timestamp=' + new Date().getTime())
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok: ' + response.statusText);
        }
        return response.json();
      })
      .then(data => {
        console.log('Bookings data received:', data);
        updateBookingsTable(data);
      })
      .catch(error => {
        console.error('Error fetching bookings:', error);
        document.getElementById('output').innerHTML = '<tr><td colspan="11" class="text-center text-danger">Error loading bookings. Please try again.</td></tr>';

        // Show error notification
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to load bookings. ' + error.message,
        });
      });
  }

    // Function to get zone data from JSON file
    function getZoneJson(date) {
        console.log('Fetching zones for date:', date);

        // Make sure we're using the correct parameter name (date)
        fetch('../api/getZone.php?date=' + date + '&timestamp=' + new Date().getTime())
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.statusText);
            }
            return response.json();
        })
        .then(data => {
            console.log('Zone data received:', data);

            // Add data to select option id="c-useZone"
            var select = document.getElementById("c-useZone");
            if (!select) {
                console.error('Zone select element not found');
                return;
            }

            // Clear existing options
            select.innerHTML = "";

            // Add default option
            // var option = document.createElement("option");
            // option.text = "Zone";
            // option.value = "";
            // select.add(option);

            // Add zone options
            // if (Array.isArray(data)) {
            //     data.forEach(function(item) {
            //         var option = document.createElement("option");
            //         option.text = item.zone_name;
            //         option.value = item.zone_id;
            //         select.add(option);
            //     });
            // } else {
            //     console.error('Zone data is not an array:', data);
            // }
        })
        .catch(error => {
            console.error('Error fetching zones:', error);

            // Show error notification
            Swal.fire({
                icon: 'warning',
                title: 'Warning',
                text: 'Failed to load zone data. ' + error.message,
            });
        });
    }

  function updateBookingsTable(bookings) {
    const tableBody = document.getElementById('output');
    tableBody.innerHTML = '';  // Clear existing rows once

    // Initialize summary totals
    let summaryTotals = {
        totalBookings: 0,
        totalGuests: 0,
        totalAmount: 0,
        totalAdult: 0,
        totalChild: 0,
        totalInfant: 0,
        totalGuide: 0,
        totalFOC: 0,
        totalTL: 0
    };

    // Check if bookings is defined and it is an array
    if (bookings && Array.isArray(bookings) && bookings.length > 0) {

        // count book_status = cancel
        let cancelCount = 0;
        bookings.forEach((booking) => {
            if (booking["book_status"] == "Cancel") {
                cancelCount++;
            }
        });

        // count book_status = Change
        let changeCount = 0;
        bookings.forEach((booking) => {
            if (booking["book_status"] == "Change") {
                changeCount++;
            }
        });

        // Update total bookings
        summaryTotals.totalBookings = bookings.length - cancelCount - changeCount;

        // Update summary totals for each booking
        bookings.forEach((booking, index) => {
            try {
                // Validate required booking properties
                if (!booking["Create Date"] || !booking["Use Date"]) {
                    console.error('Booking missing required dates:', booking);
                    return; // Skip this booking
                }

                // Calculate summary totals
                summaryTotals.totalGuests += parseInt(booking.Guests?.Adults || 0) + parseInt(booking.Guests?.Children || 0);
                summaryTotals.totalAmount += parseFloat(booking["Amount"] || 0);
                summaryTotals.totalAdult += parseInt(booking.Guests?.Adults || 0);
                summaryTotals.totalChild += parseInt(booking.Guests?.Children || 0);
                summaryTotals.totalInfant += parseInt(booking.Guests?.Infants || 0);
                summaryTotals.totalGuide += parseInt(booking.Guide || 0);
                summaryTotals.totalFOC += parseInt(booking.FOC || 0);
                summaryTotals.totalTL += parseInt(booking.TL || 0);

                var order_date = new Date(booking["Create Date"]);
                if (isNaN(order_date.getTime())) {
                    console.error('Invalid Create Date:', booking["Create Date"]);
                    order_date = new Date(); // Use current date as fallback
                }

                // Change date format to 13-03-2025 12:00:00
                var options_order = { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit' };
                // server timezone is India - Mumbai change to bangkok 
                var bangkok_date = new Date(order_date.getTime() + (7 * 60 * 60 * 1000)); // Add 7 hours to the date
                var formattedDate_order = new Intl.DateTimeFormat('en-GB', options_order).format(bangkok_date);
                // split formattedDate_order to date and time
                var [formattedDate_order_date, formattedDate_order_time] = formattedDate_order.split(' ');
                formattedDate_order_date = formattedDate_order_date.replace(/\//g, '-');
                formattedDate_order_time = formattedDate_order_time.replace(/\//g, '-');
                formattedDate_order = formattedDate_order_date + '<br/>' + formattedDate_order_time;

                // Change date booking["Use Date"] format to 13-03-2025
                var use_date = new Date(booking["Use Date"]);
                if (isNaN(use_date.getTime())) {
                    console.error('Invalid Use Date:', booking["Use Date"]);
                    use_date = new Date(); // Use current date as fallback
                }

                var options_use = { year: 'numeric', month: '2-digit', day: '2-digit' };
                var formattedDate_use = new Intl.DateTimeFormat('en-GB', options_use).format(use_date).replace(/\//g, '-');
                var bookingSpecial = "";

                // Check special_request if booking["Request"] is empty then set to "N/A"
                if (booking["Special"] == 1) {
                    bookingSpecial = `<i class="icon ti ti-cake fs-7" title="วันเกิด"></i>`;
                } else if (booking["Special"] == 2) {
                    bookingSpecial = `<i class="icon ti ti-glass-full fs-7" title="วันครบรอบ"></i>`;
                }

                // Check Booking Status
                if (booking["book_status"] == "Pending") {
                    bookingStatus = `<span class="badge bg-info">Pending</span>`;
                    bookingStatusColor = " ";
                } else if (booking["book_status"] == "Complete") {
                    bookingStatus = `<span class="badge bg-success">Complete</span>`;
                    bookingStatusColor = " ";
                } else if (booking["book_status"] == "Change") {
                    bookingStatus = `<span class="badge bg-warning">Change</span>`;
                    bookingStatusColor = "bg-warning";
                } else if (booking["book_status"] == "Cancel") {
                    bookingStatus = `<span class="badge bg-danger">Cancel</span>`;
                    bookingStatusColor = "bg-danger";
                }

                // Check Payment Status
                if (booking["Payment"] == "Paid") {
                    paymentStatus = `<span class="badge bg-success">Paid</span>`;
                    paymentStatusColor = "bg-success";
                } else if (booking["Payment"] == "WP") {
                    paymentStatus = `<span class="badge bg-warning">WP</span>`;
                    paymentStatusColor = "bg-warning";
                }


                const row = document.createElement('tr');
                row.className = 'search-items';
                row.innerHTML = `
                    <td class="${bookingStatusColor}">
                        <h6 class="user-work" data-order="${booking["Order No"]}" style="font-size: unset;">${booking["Order No"]}</h6>
                        <span class="user-datetime mb-0" style="font-size: small;">${formattedDate_order}</span>
                    </td>
                    <td class="${bookingStatusColor}">
                        <h6 class="user-customer mb-0" data-name="${booking.Customer || 'N/A'}" style="font-size: unset;">${booking.Customer || 'N/A'}</h6>
                        <span class="user-ph-no" data-phone="${booking.Phone || 'N/A'}" style="font-size: unset;">${booking.Phone || 'N/A'}</span>
                    </td>
                    <td class="${bookingStatusColor}">
                        <div class="d-flex align-items-center">
                            <a href="javascript:void(0)" style="font-size: medium !important;" data-bs-toggle="tooltip" title="Adults" class="text-bg-primary text-white fs-7 round-40 rounded-circle me-n2 card-hover border border-2 border-white d-flex align-items-center justify-content-center">${booking.Guests?.Adults || 0}</a>
                            <input type="hidden" class="user-adult" data-adult="${booking.Guests?.Adults || 0}" value="${booking.Guests?.Adults || 0}">
                            <a href="javascript:void(0)" style="font-size: medium !important;" data-bs-toggle="tooltip" title="Children" class="text-bg-success text-white fs-7 round-40 rounded-circle me-n2 card-hover border border-2 border-white d-flex align-items-center justify-content-center">${booking.Guests?.Children || 0}</a>
                            <input type="hidden" class="user-child" data-child="${booking.Guests?.Children || 0}" value="${booking.Guests?.Children || 0}">
                            <a href="javascript:void(0)" style="font-size: medium !important;" data-bs-toggle="tooltip" title="Infants" class="text-bg-danger text-white fs-7 round-40 rounded-circle me-n2 card-hover border border-2 border-white d-flex align-items-center justify-content-center">${booking.Guests?.Infants || 0}</a>
                            <input type="hidden" class="user-infant" data-infant="${booking.Guests?.Infants || 0}" value="${booking.Guests?.Infants || 0}">
                        </div>
                    </td>
                    <td class="${bookingStatusColor}">
                        <div class="d-flex align-items-center">
                            <a href="javascript:void(0)" style="font-size: medium !important;" class="text-bg-info text-white fs-7 round-40 rounded-circle me-n2 card-hover border border-2 border-white d-flex align-items-center justify-content-center">${booking.Guide || 0}</a>
                            <input type="hidden" class="user-guide" data-guide="${booking.Guide || 0}" value="${booking.Guide || 0}">
                            <a href="javascript:void(0)" style="font-size: medium !important;" class="text-bg-info text-white fs-7 round-40 rounded-circle me-n2 card-hover border border-2 border-white d-flex align-items-center justify-content-center">${booking.FOC || 0}</a>
                            <input type="hidden" class="user-foc" data-foc="${booking.FOC || 0}" value="${booking.FOC || 0}">
                            <a href="javascript:void(0)" style="font-size: medium !important;" class="text-bg-info text-white fs-7 round-40 rounded-circle me-n2 card-hover border border-2 border-white d-flex align-items-center justify-content-center">${booking.TL || 0}</a>
                            <input type="hidden" class="user-tl" data-tl="${booking.TL || 0}" value="${booking.TL || 0}">
                        </div>
                    </td>
                    <td class="${bookingStatusColor}">
                      <span class="user-floor mb-0" data-floor="${booking.Floor || ''}">${booking.Floor || ''}</span>
                    </td>
                    <td class="${bookingStatusColor}">
                      <span class="user-table mb-0" data-table="${booking.Table || ''}">${formatTableData(booking.Table) || ''}</span>
                    </td>
                    <td class="${bookingStatusColor}">
                      <span class="user-amount mb-0" data-amount="${booking["Amount"] || 0}">${booking["Amount"] || 0}</span>
                    </td>
                    <td class="${bookingStatusColor}">
                      <button class="btn btn-sm booking-status-btn ${bookingStatusColor}"
                                data-booking-id="${booking["BookID"]}"
                                data-booking="${booking["book_status"] || 'Pending'}"
                                onclick="openBookingStatusModal(this)"
                                ${(booking["book_status"] === 'Change' || booking["book_status"] === 'Cancel') ? 'disabled' : ''}>
                            ${bookingStatus}
                        </button>
                    </td>
                    <td class="${bookingStatusColor}">
                        <button class="btn btn-sm payment-status-btn ${paymentStatusColor}"
                                data-booking-id="${booking["BookID"]}"
                                data-payment="${booking["Payment"] || 'WP'}"
                                onclick="openPaymentStatusModal(this)">
                            ${paymentStatus}
                        </button>
                    </td>
                    <td class="${bookingStatusColor}">
                        <span class="user-paymentType mb-0" data-payment-type="${booking["PayType"] || 'Transfer'}">${booking["PayType"] || 'Transfer'}</span>
                    </td>
                    <td class="${bookingStatusColor}">
                      <span class="user-creditTerm mb-0" data-credit-term="${booking["Credit Term"] || ''}">${booking["Credit Term"] || ''}</span>
                    </td>
                    <td class="${bookingStatusColor}">
                      <span class="user-voucher mb-0" data-voucher="${booking["Voucher No"] || ''}">${booking["Voucher No"] || ''}</span>
                    </td>
                    <td class="${bookingStatusColor}">
                      <span class="user-agent mb-0" data-agent="${booking.Agent || ''}">${booking.Agent || ''}</span>
                    </td>
                    <td class="${bookingStatusColor}">
                        <span class="user-booking-special mb-0" data-special-request="${booking["Special"] || 0}">${bookingSpecial}</span><br/>
                        <span class="user-remark mb-1 badge text-bg-light" data-remark="${booking.Request || ''}">${booking.Request || ''}</span>
                    </td>
                    <td class="${bookingStatusColor}">
                      <span class="user-specialnote mb-0" data-specialnote="${booking.SpecialNote || ''}">${booking.SpecialNote || ''}</span>
                    </td>
                    <td class="${bookingStatusColor}">
                      <span class="mb-0">${formattedDate_use}</span><br/>
                      <button class="btn btn-sm btn-outline-info change-date-btn"
                              onclick="popupChangeDate(${booking["BookID"]})"
                              data-booking-status="${booking["book_status"] || 'Pending'}"
                              ${(booking["book_status"] === 'Change' || booking["book_status"] === 'Cancel') ? 'disabled' : ''}>
                        Change Date
                      </button>
                    </td>
                    <td class="${bookingStatusColor}">
                        <input type="hidden" class="user-bookid" data-id="${booking["BookID"]}" value="${booking["BookID"]}">
                        <input type="hidden" class="user-zoneid" data-zoneid="${booking["ZoneID"]}" value="${booking["ZoneID"]}">
                        <input type="hidden" class="user-date" data-usedate="${booking["Use Date"]}" value="${booking["Use Date"]}">
                        <input type="hidden" class="user-amount" data-amount="${booking["Amount"] || 0}" value="${booking["Amount"] || 0}">
                        <input type="hidden" class="user-orderno" data-orderno="${booking["Order No"]}" value="${booking["Order No"]}">
                        <input type="hidden" class="user-floor" data-floor="${booking["Floor"] || ''}" value="${booking["Floor"] || ''}">
                        <input type="hidden" class="user-table" data-table="${booking["Table"] || ''}" value="${booking["Table"] || ''}">
                        <input type="hidden" class="user-special-request" data-special-request="${booking["Special"] || 0}" value="${booking["Special"] || 0}">
                        <input type="hidden" class="user-name" data-name="${booking.Customer || ''}" value="${booking.Customer || ''}">
                        <input type="hidden" class="user-phone" data-phone="${booking.Phone || ''}" value="${booking.Phone || ''}">
                        <input type="hidden" class="user-voucher" data-voucher="${booking["Voucher No"] || ''}" value="${booking["Voucher No"] || ''}">
                        <input type="hidden" class="user-agent" data-agent="${booking.Agent || ''}" value="${booking.Agent || ''}">
                        <input type="hidden" class="user-remark" data-remark="${booking.Request || ''}" value="${booking.Request || ''}">
                        <input type="hidden" class="user-specialnote" data-specialnote="${booking.SpecialNote || ''}" value="${booking.SpecialNote || ''}">
                        <input type="hidden" class="user-usedate" data-usedate="${booking["Use Date"]}" value="${booking["Use Date"]}">
                        <input type="hidden" class="user-payment-type" data-payment-type="${booking["PayType"] || 'Transfer'}" value="${booking["PayType"] || 'Transfer'}">
                        <input type="hidden" class="user-credit-term" data-credit-term="${booking["Credit Term"] || ''}" value="${booking["Credit Term"] || ''}">
                        <input type="hidden" class="user-booking-status" data-booking-status="${booking["book_status"] || 'Pending'}" value="${booking["book_status"] || 'Pending'}">
                        <div class="action-btn">
                            <div class="dropdown dropstart">
                              <a href="javascript:void(0)" class="text-muted" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                                  <i class="ti ti-dots-vertical fs-6"></i>
                              </a>
                              <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                  <li class="edit-menu-item" ${isSelectedDateInPast() ? 'style="display: none;"' : ''}>
                                      <a class="edit dropdown-item d-flex align-items-center gap-3" href="javascript:void(0)">
                                        <i class="fs-4 ti ti-edit"></i>Edit
                                      </a>
                                  </li>

                                  <!--
                                  <li class="delete-menu-item" ${(booking["Payment"] === 'Paid' || booking["book_status"] === 'Complete' || isSelectedDateInPast()) ? 'style="display: none;"' : ''}>
                                      <a class="delete dropdown-item d-flex align-items-center gap-3" href="javascript:void(0)">
                                        <i class="fs-4 ti ti-trash"></i>Delete
                                      </a>
                                  </li>
                                  -->
                              </ul>
                          </div>
                        </div>
                    </td>
                `;
                tableBody.appendChild(row);
            } catch (error) {
                console.error('Error processing booking:', error, booking);
            }
        });
    } else {
        // No bookings available
        tableBody.innerHTML = '<tr><td colspan="12" class="text-center">No bookings found for this date.</td></tr>';
        console.log('No bookings available or data is not in expected format.');
    }

    // Update summary table
    updateSummaryTable(summaryTotals);

    // Update action button visibility based on payment status and date
    updateActionButtonVisibility();
}

  // Function to update summary table with calculated totals
  function updateSummaryTable(totals) {
    const summaryBody = document.getElementById('summary');

    // Format amount with currency
    const formattedAmount = new Intl.NumberFormat('th-TH', {
        style: 'currency',
        currency: 'THB',
        minimumFractionDigits: 2
    }).format(totals.totalAmount);

    // Create summary row
    const summaryRow = `
        <tr id="summary-data">
            <td class="fw-bold text-dark" style="text-align: center; font-size: x-large;">${totals.totalBookings}</td>
            <td class="fw-bold text-success" style="text-align: center; font-size: x-large;">${formattedAmount}</td>
            <td class="fw-bold text-dark" style="text-align: center; font-size: x-large;">${totals.totalGuests}</td>
            <td class="fw-bold text-primary" style="text-align: center; font-size: x-large;">${totals.totalAdult}</td>
            <td class="fw-bold text-primary" style="text-align: center; font-size: x-large;">${totals.totalChild}</td>
            <td class="fw-bold text-primary" style="text-align: center; font-size: x-large;">${totals.totalInfant}</td>
            <td class="fw-bold text-info" style="text-align: center; font-size: x-large;">${totals.totalGuide}</td>
            <td class="fw-bold text-info" style="text-align: center; font-size: x-large;">${totals.totalFOC}</td>
            <td class="fw-bold text-info" style="text-align: center; font-size: x-large;">${totals.totalTL}</td>
        </tr>
    `;

    summaryBody.innerHTML = summaryRow;

    // Update summary date display
    const selectedDate = document.getElementById('myDate').value;
    const summaryDateElement = document.getElementById('summary-date');
    if (summaryDateElement && selectedDate) {
        const formattedDate = new Date(selectedDate).toLocaleDateString('en-GB', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
        summaryDateElement.textContent = `Summary for ${formattedDate}`;
    }

    // Log summary for debugging
    console.log('Summary totals updated:', totals);
  }

  // Function to format table data for better display
  function formatTableData(tableString) {
    if (!tableString) return '';

    // Remove curly braces if present
    let cleanedString = tableString.replace(/[{}]/g, '');

    // Split by comma
    let tables = cleanedString.split(',').map(t => t.trim()).filter(t => t);

    // If there's only one table, return it with a badge based on its floor and row
    if (tables.length === 1) {
      let table = tables[0];
      let colorClass = getColorForTable(table);
      return `<span class="badge bg-${colorClass} me-1">${table}</span>`;
    }

    // For large number of tables (e.g., entire floor booking)
    if (tables.length > 50) {
      // Count tables by row
      const rowCounts = {};

      tables.forEach(table => {
        if (!table) return;

        let row = '';

        // Handle newest format (e.g., A1/1, A2/2, etc.)
        if (table.includes('/')) {
          const parts = table.split('/');
          row = parts[0]; // A1, A2, etc.
        }
        // Handle previous format (e.g., 1A01, 2B03, etc.)
        else if (table.length >= 3 && /^\d/.test(table)) {
          row = table.charAt(1);
        }
        // Handle old format (e.g., A1, B2, etc.)
        else if (table.length >= 2) {
          row = table.charAt(0);
        }

        // Initialize the count if it doesn't exist
        if (!rowCounts[row]) {
          rowCounts[row] = 0;
        }

        // Increment the count
        rowCounts[row]++;
      });

      // Create a unique ID for this collapsible section
      const uniqueId = 'tableDetails_' + Math.random().toString(36).substr(2, 9);

      // Create a summary display
      let result = '<div class="table-groups">';
      result += `<div class="d-flex align-items-center">`;
      result += `<span class="badge bg-primary me-2">Total: ${tables.length} tables</span>`;
      result += `<button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#${uniqueId}" aria-expanded="false" aria-controls="${uniqueId}">Show Details</button>`;
      result += `</div>`;

      // Add a collapsible section for the details
      result += `<div class="collapse mt-2" id="${uniqueId}">`;

      // Sort rows by name
      const sortedRows = Object.keys(rowCounts).sort();

      // Display row counts
      sortedRows.forEach(row => {
        const colorClass = getColorForRow(row);
        let rowLabel = '';

        // For new format (A1, A2, etc.)
        if (row.startsWith('A')) {
          rowLabel = `Row ${row}`;
        }
        // For older formats
        else if (row === 'V') {
          rowLabel = 'VIP Room';
        } else if (row === 'P') {
          rowLabel = 'Floor 3 Premium';
        } else if (row === 'H') {
          rowLabel = 'Floor 2 Front';
        } else {
          // rowLabel = `Row ${row}`;
          rowLabel = ``;
        }

        result += `<div class="table-group mb-1">`;
        result += `<span class="badge bg-${colorClass} me-1">${rowLabel}: ${rowCounts[row]} tables</span>`;
        result += `</div>`;
      });

      // Add all tables in a scrollable container
      result += `<div class="table-details mt-2" style="max-height: 200px; overflow-y: auto;">`;

      // Group tables by their floor and row
      const groupedTables = {};

      tables.forEach(table => {
        if (!table) return;

        let row = '';

        // Handle newest format (e.g., A1/1, A2/2, etc.)
        if (table.includes('/')) {
          const parts = table.split('/');
          row = parts[0]; // A1, A2, etc.
        }
        // Handle previous format (e.g., 1A01, 2B03, etc.)
        else if (table.length >= 3 && /^\d/.test(table)) {
          row = table.charAt(1);
        }
        // Handle old format (e.g., A1, B2, etc.)
        else if (table.length >= 2) {
          row = table.charAt(0);
        }

        // Create a group key based on row
        const groupKey = row;

        // Initialize the group if it doesn't exist
        if (!groupedTables[groupKey]) {
          groupedTables[groupKey] = [];
        }

        // Add the table to its group
        groupedTables[groupKey].push(table);
      });

      // Sort groups by row
      const sortedGroups = Object.keys(groupedTables).sort();

      sortedGroups.forEach(groupKey => {
        const colorClass = getColorForRow(groupKey);

        // Create a row for this group with a label
        result += `<div class="table-group mb-1">`;

        // Add a small label for the row
        let rowLabel = '';

        // For new format (A1, A2, etc.)
        if (groupKey.startsWith('A')) {
          rowLabel = `Row ${groupKey}`;
        }
        // For older formats
        else if (groupKey === 'V') {
          rowLabel = 'VIP Room';
        } else if (groupKey === 'P') {
          rowLabel = 'Floor 3 Premium';
        } else if (groupKey === 'H') {
          rowLabel = 'Floor 2 Front';
        } else {
          // rowLabel = `Row ${groupKey}`;
          rowLabel = ``;
        }

        result += `<small class="text-muted me-2">${rowLabel}:</small>`;

        // Add each table in this group
        groupedTables[groupKey].forEach(table => {
          result += `<span class="badge bg-${colorClass} me-1">${table}</span>`;
        });

        result += '</div>';
      });

      result += `</div>`; // Close table-details
      result += `</div>`; // Close collapse
      result += '</div>'; // Close table-groups

      return result;
    }

    // For regular number of tables (original code)
    // Group tables by their floor and row
    const groupedTables = {};

    tables.forEach(table => {
      if (!table) return;

      // Get the floor and row from the table name
      let floor = '1'; // Default to floor 1
      let row = '';

      // Handle newest format (e.g., A1/1, A2/2, etc.)
      if (table.includes('/')) {
        const parts = table.split('/');
        row = parts[0]; // A1, A2, etc.
        // For this format, all tables are on floor 1
        floor = '1';
      }
      // Handle previous format (e.g., 1A01, 2B03, etc.)
      else if (table.length >= 3 && /^\d/.test(table)) {
        floor = table.charAt(0);
        row = table.charAt(1);
      }
      // Handle old format (e.g., A1, B2, etc.)
      else if (table.length >= 2) {
        row = table.charAt(0);
        // Determine floor based on row letter
        if (row === 'A' || row === 'B') floor = '1';
        else if (row === 'C' || row === 'D' || row === 'H') floor = '2';
        else if (row === 'E' || row === 'F' || row === 'G') floor = '3';
        else if (row === 'P') floor = '3'; // Premium tables on floor 3
        else if (row === 'V') floor = 'V'; // VIP rooms
      }

      // Create a group key based on floor and row
      const groupKey = row;

      // Initialize the group if it doesn't exist
      if (!groupedTables[groupKey]) {
        groupedTables[groupKey] = [];
      }

      // Add the table to its group
      groupedTables[groupKey].push(table);
    });

    // Create HTML for each group
    let result = '<div class="table-groups">';

    // Sort groups by row
    const sortedGroups = Object.keys(groupedTables).sort();

    sortedGroups.forEach(groupKey => {
      const colorClass = getColorForRow(groupKey);

      // Create a row for this group with a label
      result += `<div class="table-group mb-1">`;

      // Add a small label for the row
      let rowLabel = '';

      // For new format (A1, A2, etc.)
      if (groupKey.startsWith('A')) {
        const rowNumber = groupKey.substring(1);
        rowLabel = `Row ${groupKey}`;
      }
      // For older formats
      else if (groupKey === 'V') {
        rowLabel = 'VIP Room';
      } else if (groupKey === 'P') {
        rowLabel = 'Floor 3 Premium';
      } else if (groupKey === 'H') {
        rowLabel = 'Floor 2 Front';
      } else {
        // rowLabel = `Row ${groupKey}`;
        rowLabel = ``;
      }
      
      // if(rowLabel !== ''){
        result += ``;
      // }else{
      //   result += `<small class="text-muted me-2">${rowLabel}:</small>`;
      // }

      // Add each table in this group
      groupedTables[groupKey].forEach(table => {
        result += `<span class="badge bg-${colorClass} me-1">${table}</span>`;
      });

      result += '</div>';
    });

    result += '</div>';
    return result;
  }

  // Helper function to determine color based on row letter or row identifier
  function getColorForRow(row) {
    // For new format (e.g., A1, A2, etc.)
    if (row.startsWith('A')) {
      const rowNumber = parseInt(row.substring(1), 10);

      // Map row numbers to colors
      switch(rowNumber) {
        case 1: return 'success';  // Green
        case 2: return 'warning';  // Yellow
        case 3: return 'danger';   // Red
        case 4: return 'info';     // Light Blue
        case 5: return 'primary';  // Blue
        case 6: return 'secondary'; // Gray
        case 7: return 'dark';     // Dark
        case 8: return 'purple';   // Purple
        default: return 'primary'; // Default to blue
      }
    }

    // For older formats, use the original color mapping
    const colorMap = {
      'A': 'success',  // Green
      'B': 'warning',  // Yellow
      'C': 'danger',   // Red
      'D': 'info',     // Light Blue
      'E': 'primary',  // Blue
      'F': 'secondary', // Gray
      'G': 'dark',     // Dark
      'H': 'purple',   // Purple for front zone
      'P': 'purple',   // Purple for premium tables
      'V': 'pink'      // Pink for VIP rooms
    };

    // Return the mapped color or a default
    return colorMap[row] || 'primary';
  }

  // Helper function to determine color based on full table name
  function getColorForTable(table) {
    // For newest format (e.g., A1/1, A2/2, etc.)
    if (table.includes('/')) {
      const parts = table.split('/');
      const row = parts[0]; // A1, A2, etc.
      return getColorForRow(row);
    }
    // For previous format (e.g., 1A01, 2B03)
    else if (table.length >= 3 && /^\d/.test(table)) {
      const row = table.charAt(1).toUpperCase();
      return getColorForRow(row);
    }
    // For old format (e.g., A1, B2)
    else if (table.length >= 2) {
      const row = table.charAt(0).toUpperCase();
      return getColorForRow(row);
    }

    return 'primary'; // Default color
  }

  // Add event listener for date changes
  document.getElementById('myDate').addEventListener('change', formatAndDisplayDate);

  // Call the function on page load
  // Initialize when DOM is fully loaded
  document.addEventListener('DOMContentLoaded', function() {
    // Set the date input to today's date
    const today = new Date();
    const day = String(today.getDate()).padStart(2, '0');
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const year = today.getFullYear();

    // Set the date input value
    document.getElementById('myDate').value = `${year}-${month}-${day}`;

    // Format and display the date (this will also fetch bookings)
    formatAndDisplayDate();

    // Get the formatted date for API calls
    const formattedDate = `${day}-${month}-${year}`;

    // Fetch bookings for today
    console.log('Initial load - fetching bookings for today:', formattedDate);
    fetchBookingsByDate(formattedDate);
    getZoneJson(formattedDate);

    // Update delete button visibility after a short delay to ensure DOM is ready
    setTimeout(updateDeleteButtonVisibility, 1000);

    // Initialize tooltips for dynamically added elements
    /*
    document.addEventListener('mouseover', function(e) {
      // Check if the element or its parent has a badge class
      const target = e.target.closest('.badge');
      if (target && !target.hasAttribute('data-bs-toggle')) {
        // Get the table name
        const tableName = target.textContent.trim();

        // Create descriptive tooltip
        let tooltipText = 'Table: ' + tableName;

        // For newest format (e.g., A1/1, A2/2, etc.)
        if (tableName.includes('/')) {
          const parts = tableName.split('/');
          const row = parts[0]; // A1, A2, etc.
          const tableNumber = parts[1];

          if (row.startsWith('A')) {
            const rowNumber = row.substring(1);
            tooltipText += ` - Floor 1, Row ${row}, Table ${tableNumber}`;
          }
        }
        // For previous format (e.g., 1A01, 2B03)
        else if (tableName.length >= 3 && /^\d/.test(tableName)) {
          const floor = tableName.charAt(0);
          const row = tableName.charAt(1).toUpperCase();

          if (row === 'H') {
            tooltipText += ' - Floor 2 Front Zone';
          } else if (row === 'P') {
            tooltipText += ' - Floor 3 Premium Table';
          } else {
            tooltipText += ` - Floor ${floor}, Row ${row}`;
          }
        }
        // For old format (e.g., A1, B2)
        else if (tableName.length >= 2) {
          const prefix = tableName.charAt(0).toUpperCase();

          // Add additional information based on prefix
          switch(prefix) {
            case 'A':
              tooltipText += ' - Floor 1, Row A';
              break;
            case 'B':
              tooltipText += ' - Floor 1, Row B';
              break;
            case 'C':
              tooltipText += ' - Floor 2, Row A';
              break;
            case 'D':
              tooltipText += ' - Floor 2, Row B';
              break;
            case 'E':
              tooltipText += ' - Floor 3, Row A';
              break;
            case 'F':
              tooltipText += ' - Floor 3, Row B';
              break;
            case 'G':
              tooltipText += ' - Floor 3, Row C';
              break;
            case 'H':
              tooltipText += ' - Floor 2, Front Zone';
              break;
            case 'P':
              tooltipText += ' - Floor 3, Premium Table';
              break;
            case 'V':
              tooltipText += ' - VIP Room';
              break;
          }
        }

        // Add tooltip attributes
        target.setAttribute('data-bs-toggle', 'tooltip');
        target.setAttribute('data-bs-placement', 'top');
        target.setAttribute('data-bs-title', tooltipText);

        // Initialize the tooltip
        new bootstrap.Tooltip(target);
      }
    });
    */
  });

  // Excel Export functionality
  document.getElementById('btn-export-excel').addEventListener('click', function() {
    // Get the current selected date
    const dateInput = document.getElementById('myDate');
    const selectedDate = dateInput.value;

    if (!selectedDate) {
      Swal.fire({
        icon: 'warning',
        title: 'No Date Selected',
        text: 'Please select a date to export bookings.',
      });
      return;
    }

    // Check if there are any visible bookings to export
    const visibleRows = document.querySelectorAll('.search-table .search-items:not(.header-item)');
    const visibleBookings = Array.from(visibleRows).filter(row => {
      return window.getComputedStyle(row).display !== 'none';
    });

    if (visibleBookings.length === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'No Data to Export',
        text: 'No bookings are currently visible to export. Please adjust your search or date selection.',
      });
      return;
    }

    // Convert date format from yyyy-mm-dd to dd-mm-yyyy
    const dateParts = selectedDate.split('-');
    const formattedDate = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;

    // Get search filter value
    const searchValue = document.getElementById('input-search').value;

    // Show loading message
    Swal.fire({
      title: 'Exporting...',
      text: `Preparing Excel file for ${visibleBookings.length} booking(s)`,
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    // Create download link with search parameter
    let exportUrl = `export_excel.php?date=${encodeURIComponent(formattedDate)}`;
    if (searchValue.trim()) {
      exportUrl += `&search=${encodeURIComponent(searchValue.trim())}`;
    }

    // Create a temporary link element and trigger download
    const link = document.createElement('a');
    link.href = exportUrl;
    const filename = searchValue.trim() ?
      `Bookings_${formattedDate.replace(/-/g, '_')}_filtered.xls` :
      `Bookings_${formattedDate.replace(/-/g, '_')}.xls`;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Close loading message after a short delay
    setTimeout(() => {
      Swal.close();
      Swal.fire({
        icon: 'success',
        title: 'Export Complete',
        text: `Excel file with ${visibleBookings.length} booking(s) has been downloaded successfully!`,
        timer: 2000,
        showConfirmButton: false
      });
    }, 1000);
  });

  // Print PDF functionality using mpdf 1st floor
  document.getElementById('btn-print-pdf-1st-floor').addEventListener('click', function() {
    // Get the current selected date
    const dateInput = document.getElementById('myDate');
    const selectedDate = dateInput.value;

    if (!selectedDate) {
      Swal.fire({
        icon: 'warning',
        title: 'No Date Selected',
        text: 'Please select a date to print bookings.',
      });
      return;
    }

    // Check if there are any visible bookings to print
    const visibleRows = document.querySelectorAll('.search-table .search-items:not(.header-item)');
    const visibleBookings = Array.from(visibleRows).filter(row => {
      return window.getComputedStyle(row).display !== 'none';
    });

    if (visibleBookings.length === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'No Data to Print',
        text: 'No bookings are currently visible to print. Please adjust your search or date selection.',
      });
      return;
    }

    // Convert date format from yyyy-mm-dd to dd-mm-yyyy
    const dateParts = selectedDate.split('-');
    const formattedDate = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;

    // Get search filter value
    const searchValue = document.getElementById('input-search').value;

    // Show loading message
    Swal.fire({
      title: 'Generating PDF...',
      text: `Preparing PDF file for ${visibleBookings.length} booking(s)`,
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    // Create PDF URL with search parameter
    let pdfUrl = `export_pdf_1st_floor.php?date=${encodeURIComponent(formattedDate)}`;
    if (searchValue.trim()) {
      pdfUrl += `&search=${encodeURIComponent(searchValue.trim())}`;
    }

    // Open PDF download URL
    window.location.href = pdfUrl;

    // Close loading message after a short delay
    setTimeout(() => {
      Swal.close();
      Swal.fire({
        icon: 'success',
        title: 'PDF Download Started',
        text: `PDF with ${visibleBookings.length} booking(s) download has been initiated.`,
        timer: 3000,
        showConfirmButton: false
      });
    }, 1000);
  });

  // Print PDF functionality using mpdf 2nd floor
  document.getElementById('btn-print-pdf-2nd-floor').addEventListener('click', function() {
    // Get the current selected date
    const dateInput = document.getElementById('myDate');
    const selectedDate = dateInput.value;

    if (!selectedDate) {
      Swal.fire({
        icon: 'warning',
        title: 'No Date Selected',
        text: 'Please select a date to print bookings.',
      });
      return;
    }
    // Check if there are any visible bookings to print
    const visibleRows = document.querySelectorAll('.search-table .search-items:not(.header-item)');
    const visibleBookings = Array.from(visibleRows).filter(row => {
      return window.getComputedStyle(row).display !== 'none';
    });

    if (visibleBookings.length === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'No Data to Print',
        text: 'No bookings are currently visible to print. Please adjust your search or date selection.',
      });
      return;
    }

    // Convert date format from yyyy-mm-dd to dd-mm-yyyy
    const dateParts = selectedDate.split('-');
    const formattedDate = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;

    // Get search filter value
    const searchValue = document.getElementById('input-search').value;

    // Show loading message
    Swal.fire({
      title: 'Generating PDF...',
      text: `Preparing PDF file for ${visibleBookings.length} booking(s)`,
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    // Create PDF URL with search parameter
    let pdfUrl = `export_pdf_2nd_floor.php?date=${encodeURIComponent(formattedDate)}`;
    if (searchValue.trim()) {
      pdfUrl += `&search=${encodeURIComponent(searchValue.trim())}`;
    }

    // Open PDF download URL
    window.location.href = pdfUrl;
    // Close loading message after a short delay
    setTimeout(() => {
      Swal.close();
      Swal.fire({
        icon: 'success',
        title: 'PDF Download Started',
        text: `PDF with ${visibleBookings.length} booking(s) download has been initiated.`,
        timer: 3000,
        showConfirmButton: false
      });
    }, 1000);
  });

  // Print PDF functionality using mpdf 3rd floor
  document.getElementById('btn-print-pdf-3rd-floor').addEventListener('click', function() {
    // Get the current selected date
    const dateInput = document.getElementById('myDate');
    const selectedDate = dateInput.value;

    if (!selectedDate) {
      Swal.fire({
        icon: 'warning',
        title: 'No Date Selected',
        text: 'Please select a date to print bookings.',
      });
      return;
    }

    // Check if there are any visible bookings to print
    const visibleRows = document.querySelectorAll('.search-table .search-items:not(.header-item)');
    const visibleBookings = Array.from(visibleRows).filter(row => {
      return window.getComputedStyle(row).display !== 'none';
    });

    if (visibleBookings.length === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'No Data to Print',
        text: 'No bookings are currently visible to print. Please adjust your search or date selection.',
      });
      return;
    }

    // Convert date format from yyyy-mm-dd to dd-mm-yyyy
    const dateParts = selectedDate.split('-');
    const formattedDate = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;

    // Get search filter value
    const searchValue = document.getElementById('input-search').value;

    // Show loading message
    Swal.fire({
      title: 'Generating PDF...',
      text: `Preparing PDF file for ${visibleBookings.length} booking(s)`,
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    // Create PDF URL with search parameter
    let pdfUrl = `export_pdf_3rd_floor.php?date=${encodeURIComponent(formattedDate)}`;
    if (searchValue.trim()) {
      pdfUrl += `&search=${encodeURIComponent(searchValue.trim())}`;
    }

    // Open PDF download URL
    window.location.href = pdfUrl;

    // Close loading message after a short delay
    setTimeout(() => {
      Swal.close();
      Swal.fire({
        icon: 'success',
        title: 'PDF Download Started',
        text: `PDF with ${visibleBookings.length} booking(s) download has been initiated.`,
        timer: 3000,
        showConfirmButton: false
      });
    }, 1000);
  });


  // Print PDF functionality using mpdf all floor
  document.getElementById('btn-print-pdf-all').addEventListener('click', function() {
    // Get the current selected date
    const dateInput = document.getElementById('myDate');
    const selectedDate = dateInput.value;

    if (!selectedDate) {
      Swal.fire({
        icon: 'warning',
        title: 'No Date Selected',
        text: 'Please select a date to print bookings.',
      });
      return;
    }

    // Check if there are any visible bookings to print
    const visibleRows = document.querySelectorAll('.search-table .search-items:not(.header-item)');
    const visibleBookings = Array.from(visibleRows).filter(row => {
      return window.getComputedStyle(row).display !== 'none';
    });

    if (visibleBookings.length === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'No Data to Print',
        text: 'No bookings are currently visible to print. Please adjust your search or date selection.',
      });
      return;
    }

    // Convert date format from yyyy-mm-dd to dd-mm-yyyy
    const dateParts = selectedDate.split('-');
    const formattedDate = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;

    // Get search filter value
    const searchValue = document.getElementById('input-search').value;

    // Show loading message
    Swal.fire({
      title: 'Generating PDF...',
      text: `Preparing PDF file for ${visibleBookings.length} booking(s)`,
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    // Create PDF URL with search parameter
    let pdfUrl = `export_pdf_all.php?date=${encodeURIComponent(formattedDate)}`;
    if (searchValue.trim()) {
      pdfUrl += `&search=${encodeURIComponent(searchValue.trim())}`;
    }

    // Open PDF download URL
    window.location.href = pdfUrl;

    // Close loading message after a short delay
    setTimeout(() => {
      Swal.close();
      Swal.fire({
        icon: 'success',
        title: 'PDF Download Started',
        text: `PDF with ${visibleBookings.length} booking(s) download has been initiated.`,
        timer: 3000,
        showConfirmButton: false
      });
    }, 1000);
  });


  // Initialize datepicker for edit modal
  function initializeEditDatePicker() {
    // Remove any existing datepicker to avoid conflicts
    /*
    try {
      $('#edit-date').datepicker('destroy');
    } catch (e) {
      // Ignore if no existing datepicker
    }

    // Initialize the datepicker
    $('#edit-date').datepicker({
      format: 'yyyy-mm-dd',
      autoclose: true,
      todayHighlight: false,
      todayBtn: 'linked',
      clearBtn: false,
      orientation: 'top auto',
      language: 'en',
      zIndexOffset: 2000 // Higher z-index for modal
    });

    // Make the date field editable when datepicker is initialized
    $('#edit-date').prop('readonly', false);

    // Handle calendar button click
    $('#edit-date-picker-btn').off('click').on('click', function(e) {
      e.preventDefault();
      $('#edit-date').datepicker('show');
    });

    // Handle date selection
    $('#edit-date').off('changeDate').on('changeDate', function(e) {
      console.log('Edit date changed to:', $(this).val());
      // You can add additional validation or processing here if needed
    });
    */
  }

  // Function to check if the selected date is in the past
  function isSelectedDateInPast() {
    const selectedDate = document.getElementById('myDate').value;
    if (!selectedDate) return false;

    // Parse the selected date (format: YYYY-MM-DD)
    const selected = new Date(selectedDate);
    const today = new Date();

    // Set time to start of day for accurate comparison
    selected.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);

    return selected < today;
  }

  // Function to hide edit/delete buttons based on conditions
  function updateActionButtonVisibility() {
    const isPastDate = isSelectedDateInPast();

    // Find all rows in the booking table
    const rows = document.querySelectorAll('#output tr');

    rows.forEach(row => {
      const editMenuItem = row.querySelector('.edit-menu-item');
      const deleteMenuItem = row.querySelector('.delete-menu-item');
      const paymentButton = row.querySelector('.payment-status-btn');
      const bookingButton = row.querySelector('.booking-status-btn');
      const changeDateButton = row.querySelector('.change-date-btn');

      if (editMenuItem && bookingButton) {
        const bookingStatus = bookingButton.getAttribute('data-booking');

        // Hide edit button for past dates or Change/Cancel status
        if (isPastDate || bookingStatus === 'Change' || bookingStatus === 'Cancel') {
          editMenuItem.style.display = 'none';
        } else {
          editMenuItem.style.display = 'block';
        }
      }

      if (deleteMenuItem && paymentButton && bookingButton) {
        const paymentStatus = paymentButton.getAttribute('data-payment');
        const bookingStatus = bookingButton.getAttribute('data-booking');

        // Hide delete button for paid bookings, complete bookings, or past dates
        if (paymentStatus === 'Paid' || bookingStatus === 'Complete' || isPastDate) {
          deleteMenuItem.style.display = 'none';
        } else {
          deleteMenuItem.style.display = 'block';
        }
      }

      // Handle booking status button and change date button
      if (bookingButton) {
        const bookingStatus = bookingButton.getAttribute('data-booking');

        // Disable booking status button for Change or Cancel status
        if (bookingStatus === 'Change' || bookingStatus === 'Cancel') {
          bookingButton.disabled = true;
          bookingButton.style.cursor = 'not-allowed';
          bookingButton.style.opacity = '0.6';
          bookingButton.title = 'Cannot change status for bookings with Change or Cancel status';
        } else {
          bookingButton.disabled = false;
          bookingButton.style.cursor = 'pointer';
          bookingButton.style.opacity = '1';
          bookingButton.title = '';
        }
      }

      if (changeDateButton) {
        const bookingStatus = changeDateButton.getAttribute('data-booking-status');

        // Disable change date button for Change or Cancel status
        if (bookingStatus === 'Change' || bookingStatus === 'Cancel') {
          changeDateButton.disabled = true;
          changeDateButton.style.cursor = 'not-allowed';
          changeDateButton.style.opacity = '0.6';
          changeDateButton.title = 'Cannot change date for bookings with Change or Cancel status';
        } else {
          changeDateButton.disabled = false;
          changeDateButton.style.cursor = 'pointer';
          changeDateButton.style.opacity = '1';
          changeDateButton.title = 'Change booking date';
        }
      }
    });
  }

  // Keep the old function name for backward compatibility
  function updateDeleteButtonVisibility() {
    updateActionButtonVisibility();
  }

  // Booking Status Functions
  function openBookingStatusModal(button) {
    // Check if the button is disabled (for Change or Cancel status)
    if (button.disabled) {
      Swal.fire({
        icon: 'warning',
        title: 'Cannot Change Status',
        text: 'Cannot change status for bookings with Change or Cancel status',
        timer: 3000,
        showConfirmButton: false
      });
      return;
    }

    const bookingId = button.getAttribute('data-booking-id');
    const currentStatus = button.getAttribute('data-booking');

    // Reset form
    document.getElementById('bookingStatusId').value = bookingId;
    document.getElementById('bookingStatus').value = currentStatus || 'Pending';

    const modal = new bootstrap.Modal(document.getElementById('bookingStatusModal'));
    modal.show();
  }

  // updateBookingStatus function
  function updateBookingStatus() {
    const bookingId = document.getElementById('bookingStatusId').value;
    const bookingStatus = document.getElementById('bookingStatus').value;
    const bookingNote = document.getElementById('bookingNoteChange').value;

    // Show loading
    Swal.fire({
      title: 'Updating Booking Status...',
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    // Send request
    $.ajax({
      url: 'api/update_booking_status.php',
      method: 'POST',
      data: {
        booking_id: bookingId,
        booking_status: bookingStatus,
        booking_note: bookingNote
      },
      dataType: 'json',
      success: function(response) {
        Swal.close();

        if (response.success) {
          Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: 'Booking status updated successfully',
            timer: 2000,
            showConfirmButton: false
          });

          // Close modal and refresh the booking list
          const modal = bootstrap.Modal.getInstance(document.getElementById('bookingStatusModal'));
          modal.hide();

          // Refresh the current date's bookings
          const currentDate = document.getElementById('myDate').value;
          if (currentDate) {
            fetchBookingsByDate(currentDate);
          }
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Error!',
            text: response.message || 'Failed to update booking status'
          });
        }
      },
      error: function() {
        Swal.close();
        Swal.fire({
          icon: 'error',
          title: 'Error!',
          text: 'Failed to update booking status'
        });
      }
    });
  }


  // Payment Status Functions
  function openPaymentStatusModal(button) {
    const bookingId = button.getAttribute('data-booking-id');
    const currentStatus = button.getAttribute('data-payment');

    // Reset form
    document.getElementById('paymentBookingId').value = bookingId;
    document.getElementById('paymentStatus').value = currentStatus || 'WP';
    document.getElementById('paymentNote').value = '';
    document.getElementById('paymentReference').value = '';
    document.getElementById('previewImagesContainer').innerHTML = '';
    document.getElementById('imagePreview').style.display = 'none';
    document.getElementById('existingAttachments').style.display = 'none';
    document.getElementById('existingImagesContainer').innerHTML = '';

    // If status is "Paid", fetch existing payment details
    if (currentStatus === 'Paid') {
      fetch(`api/get_payment_details.php?booking_id=${bookingId}`)
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // Fill in existing note
            if (data.payment_note) {
              document.getElementById('paymentNote').value = data.payment_note;
            }

            // Show existing attachments if available
            if (data.attachments && data.attachments.length > 0) {
              displayExistingAttachments(data.attachments);
            }
          }
        })
        .catch(error => {
          console.error('Error fetching payment details:', error);
        });
    }

    const modal = new bootstrap.Modal(document.getElementById('paymentStatusModal'));
    modal.show();
  }

  // Function to display existing attachments
  function displayExistingAttachments(attachments) {
    const container = document.getElementById('existingImagesContainer');
    container.innerHTML = '';

    attachments.forEach((attachment, index) => {
      const imageDiv = document.createElement('div');
      imageDiv.className = 'col-md-4 mb-2';
      imageDiv.innerHTML = `
        <div class="card">
          <img src="${attachment.reference_image}" class="card-img-top" style="height: 150px; object-fit: cover;" alt="Payment Reference ${index + 1}">
          <div class="card-body p-2">
            <small class="text-muted">Uploaded: ${new Date(attachment.created_at).toLocaleDateString()}</small>
            <div class="mt-1">
              <a href="${attachment.reference_image}" target="_blank" class="btn btn-sm btn-outline-primary">View Full</a>
              ${attachment.id > 0 ? `<button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteExistingAttachment(${attachment.id})">Delete</button>` : '<small class="text-muted">(Legacy attachment)</small>'}
            </div>
          </div>
        </div>
      `;
      container.appendChild(imageDiv);
    });

    document.getElementById('existingAttachments').style.display = 'block';
  }

  // Image preview function for multiple files
  document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('paymentReference');
    if (fileInput) {
      fileInput.addEventListener('change', function(e) {
        const files = Array.from(e.target.files);
        const container = document.getElementById('previewImagesContainer');
        container.innerHTML = '';

        if (files.length === 0) {
          document.getElementById('imagePreview').style.display = 'none';
          return;
        }

        // Validate number of files (max 5)
        if (files.length > 5) {
          Swal.fire({
            icon: 'error',
            title: 'Too Many Files',
            text: 'Please select maximum 5 images'
          });
          e.target.value = '';
          return;
        }

        let validFiles = [];
        let processedCount = 0;

        files.forEach((file, index) => {
          // Validate file size (5MB max)
          if (file.size > 5 * 1024 * 1024) {
            Swal.fire({
              icon: 'error',
              title: 'File Too Large',
              text: `File "${file.name}" is too large. Please select images smaller than 5MB`
            });
            e.target.value = '';
            return;
          }

          // Validate file type
          if (!file.type.startsWith('image/')) {
            Swal.fire({
              icon: 'error',
              title: 'Invalid File Type',
              text: `File "${file.name}" is not an image. Please select image files (JPG, PNG, GIF)`
            });
            e.target.value = '';
            return;
          }

          validFiles.push(file);

          // Show preview
          const reader = new FileReader();
          reader.onload = function(e) {
            const imageDiv = document.createElement('div');
            imageDiv.className = 'col-md-4 mb-2';
            imageDiv.innerHTML = `
              <div class="card">
                <img src="${e.target.result}" class="card-img-top" style="height: 150px; object-fit: cover;" alt="Preview ${index + 1}">
                <div class="card-body p-2">
                  <small class="text-muted">${file.name}</small>
                  <div class="mt-1">
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removePreviewImage(${index})">Remove</button>
                  </div>
                </div>
              </div>
            `;
            container.appendChild(imageDiv);

            processedCount++;
            if (processedCount === validFiles.length) {
              document.getElementById('imagePreview').style.display = 'block';
            }
          };
          reader.readAsDataURL(file);
        });
      });
    }
  });

  function removeAllImages() {
    document.getElementById('paymentReference').value = '';
    document.getElementById('imagePreview').style.display = 'none';
    document.getElementById('previewImagesContainer').innerHTML = '';
  }

  function removePreviewImage(index) {
    const fileInput = document.getElementById('paymentReference');
    const files = Array.from(fileInput.files);

    // Create new FileList without the removed file
    const dt = new DataTransfer();
    files.forEach((file, i) => {
      if (i !== index) {
        dt.items.add(file);
      }
    });

    fileInput.files = dt.files;

    // Trigger change event to refresh preview
    fileInput.dispatchEvent(new Event('change'));
  }

  function deleteExistingAttachment(attachmentId) {
    Swal.fire({
      title: 'Delete Attachment?',
      text: 'This action cannot be undone.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed) {
        // Show loading
        Swal.fire({
          title: 'Deleting...',
          allowOutsideClick: false,
          didOpen: () => {
            Swal.showLoading();
          }
        });

        fetch('api/delete_payment_attachment.php', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            attachment_id: attachmentId
          })
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            Swal.fire({
              icon: 'success',
              title: 'Deleted!',
              text: 'Attachment has been deleted.',
              timer: 2000,
              showConfirmButton: false
            });

            // Refresh the modal to show updated attachments
            const bookingId = document.getElementById('paymentBookingId').value;
            const button = document.querySelector(`[data-booking-id="${bookingId}"]`);
            if (button) {
              openPaymentStatusModal(button);
            }
          } else {
            Swal.fire({
              icon: 'error',
              title: 'Error!',
              text: data.message || 'Failed to delete attachment'
            });
          }
        })
        .catch(error => {
          console.error('Error:', error);
          Swal.fire({
            icon: 'error',
            title: 'Error!',
            text: 'Failed to delete attachment'
          });
        });
      }
    });
  }

  function updatePaymentStatus() {
    const bookingId = document.getElementById('paymentBookingId').value;
    const paymentStatus = document.getElementById('paymentStatus').value;
    const note = document.getElementById('paymentNote').value;
    const fileInput = document.getElementById('paymentReference');

    // Show loading
    Swal.fire({
      title: 'Updating Payment Status...',
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    // Create FormData to handle multiple file uploads
    const formData = new FormData();
    formData.append('booking_id', bookingId);
    formData.append('payment_status', paymentStatus);
    formData.append('note', note);

    // Add multiple files if selected
    if (fileInput.files.length > 0) {
      for (let i = 0; i < fileInput.files.length; i++) {
        formData.append('payment_references[]', fileInput.files[i]);
      }
    }

    // Send AJAX request to update payment status
    fetch('api/update_payment_status.php', {
      method: 'POST',
      body: formData
    })
    .then(response => response.json())
    .then(data => {
      Swal.close();

      if (data.success) {
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('paymentStatusModal'));
        modal.hide();

        // Update payment status button appearance
        const paymentButton = document.querySelector(`.payment-status-btn[data-booking-id="${bookingId}"]`);
        console.log('Updating payment button for booking ID:', bookingId, 'New status:', paymentStatus);
        console.log('Found payment button:', paymentButton);

        if (paymentButton) {
          // Update data attribute
          paymentButton.setAttribute('data-payment', paymentStatus);

          // Update button content with proper badge HTML
          let paymentStatusHTML = '';
          let buttonClass = 'btn btn-sm payment-status-btn ';

          if (paymentStatus === 'Paid') {
            paymentStatusHTML = '<span class="badge bg-success">Paid</span>';
            buttonClass += 'btn-success';
          } else if (paymentStatus === 'WP') {
            paymentStatusHTML = '<span class="badge bg-warning">WP</span>';
            buttonClass += 'btn-warning';
          } else {
            paymentStatusHTML = `<span class="badge bg-secondary">${paymentStatus}</span>`;
            buttonClass += 'btn-secondary';
          }

          // Update button HTML and class
          paymentButton.innerHTML = paymentStatusHTML;
          paymentButton.className = buttonClass;

          // Update the table cell background color to match the payment status
          const tableCell = paymentButton.closest('td');
          if (tableCell) {
            // Remove existing background classes
            tableCell.classList.remove('bg-success', 'bg-warning', 'bg-secondary');

            // Add new background class based on payment status
            if (paymentStatus === 'Paid') {
              tableCell.classList.add('bg-success');
            } else if (paymentStatus === 'WP') {
              tableCell.classList.add('bg-warning');
            } else {
              tableCell.classList.add('bg-secondary');
            }
          }

          // Update action button visibility after payment status change
          updateActionButtonVisibility();
        } else {
          console.error('Payment status button not found for booking ID:', bookingId);
          // Fallback: refresh the entire booking list to ensure UI is updated
          const currentDate = document.getElementById('myDate').value;
          if (currentDate) {
            console.log('Refreshing booking list as fallback...');
            fetchBookingsByDate(currentDate);
          }
        }

        // Show success message
        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: 'Payment status updated successfully',
          timer: 2000,
          showConfirmButton: false
        });
      } else {
        Swal.fire({
          icon: 'error',
          title: 'Error!',
          text: data.message || 'Failed to update payment status'
        });
      }
    })
    .catch(error => {
      Swal.close();
      console.error('Error:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error!',
        text: 'An error occurred while updating payment status'
      });
    });
  }

  // Edit booking functionality
  $(document).on('click', '.edit', function() {
    const row = $(this).closest('tr');

    // Check booking status before allowing edit
    const bookingStatus = row.find('.user-booking-status').val() || 'Pending';
    if (bookingStatus === 'Change' || bookingStatus === 'Cancel') {
      Swal.fire({
        icon: 'warning',
        title: 'Cannot Edit Booking',
        text: 'Cannot edit bookings with Change or Cancel status',
        timer: 3000,
        showConfirmButton: false
      });
      return;
    }

    // Extract booking data from the row
    const bookingData = {
      id: row.find('.user-orderid').val(),
      bookingid: row.find('.user-bookid').val(),
      orderNo: row.find('.user-orderno').val(),
      customerName: row.find('.user-name').val(),
      phone: row.find('.user-phone').val(),
      adults: row.find('.user-adult').val() || 0,
      children: row.find('.user-child').val() || 0,
      infants: row.find('.user-infant').val() || 0,
      guide: row.find('.user-guide').val() || 0,
      foc: row.find('.user-foc').val() || 0,
      leader: row.find('.user-tl').val() || 0,
      voucher: row.find('.user-voucher').data('voucher') || row.find('.user-voucher').val(),
      agent: row.find('.user-agent').data('agent') || row.find('.user-agent').val(),
      amount: row.find('.user-amount').data('amount') || row.find('.user-amount').val() || 0,
      remark: row.find('.user-remark').data('remark') || row.find('.user-remark').val(),
      specialNote: row.find('.user-specialnote').data('specialnote') || row.find('.user-specialnote').val(),
      date: row.find('.user-usedate').val(),
      floor: row.find('.user-floor').data('floor') || row.find('.user-floor').val(),
      tables: row.find('.user-table').data('table'),
      specialRequest: row.find('.user-special-request').val() || 0,
      paymentType: row.find('.user-payment-type').val() || 'Transfer',
      creditTerm: row.find('.user-credit-term').val() || '',
      bookingStatus: row.find('.user-booking-status').val() || 'Pending'
    };


    // Populate the edit modal
    populateEditModal(bookingData);

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('editBookingModal'));
    modal.show();
  });

  // Function to populate edit modal with booking data
  function populateEditModal(data) {
    // console.log('Populating edit modal with data:', data);

    // Basic fields
    $('#edit-booking-id').val(data.id);
    $('#data-id').val(data.bookingid);
    $('#edit-order-no').val(data.orderNo);
    $('#edit-customer-name').val(data.customerName);
    $('#edit-phone').val(data.phone);
    $('#edit-adults').val(data.adults);
    $('#edit-children').val(data.children);
    $('#edit-infants').val(data.infants);
    $('#edit-guide').val(data.guide);
    $('#edit-foc').val(data.foc);
    $('#edit-leader').val(data.leader);
    $('#edit-voucher').val(data.voucher);
    $('#edit-amount').val(data.amount);
    $('#edit-remark').val(data.remark);
    $('#edit-specialnote').val(data.specialNote);
    $('#edit-date').val(data.date);
    $('#edit-booking-status').text(data.bookingStatus);

    // Initialize datepicker for edit date field
    initializeEditDatePicker();

    // Set booking status radio button
    $(`input[name="edit_booking_status"][value="${data.bookingStatus}"]`).prop('checked', true);

    // Set payment type radio button
    if(data.paymentType === 'Cash') {
      $('#edit-payment-cash').prop('checked', true);
    } else if (data.paymentType === 'Credit Card') {
      $('#edit-payment-credit').prop('checked', true);
    } else {
      $('#edit-payment-transfer').prop('checked', true);
    }

    // Set special request radio button
    $(`input[name="edit_special_request"][value="${data.specialRequest}"]`).prop('checked', true);

    // Load agents and set selected agent
    loadAgentsForEdit(data.agent);

    // Display selected tables
    displaySelectedTablesInEdit(data.tables);
  }

  // Function to load agents for edit modal
  function loadAgentsForEdit(selectedAgent) {
    $.ajax({
      url: '../agent/get_agents.php',
      method: 'GET',
      dataType: 'json',
      success: function(agents) {
        const agentSelect = $('#edit-agent');
        agentSelect.empty();
        agentSelect.append('<option value="">Select Agent</option>');

        agents.forEach(function(agent) {
          const selected = agent.agent_name === selectedAgent ? 'selected' : '';
          agentSelect.append(`<option value="${agent.agent_name}" ${selected}>${agent.agent_name}</option>`);
        });
      },
      error: function() {
        console.error('Failed to load agents');
      }
    });
  }

  // Function to display selected tables in edit modal
  function displaySelectedTablesInEdit(tablesString) {
    const tablesContainer = $('#edit-selected-tables');
    tablesContainer.empty();

    if (tablesString) {
      // Parse tables string (e.g., "A11,A21,B11" or "{A11,A21,B11}")
      let tables = tablesString.replace(/[{}]/g, '').split(',');

      tables.forEach(function(table) {
        const trimmedTable = table.trim();
        if (trimmedTable) {
          // Get color based on table prefix
          const color = getTableColor(trimmedTable);
          tablesContainer.append(`
            <span class="badge bg-${color} me-1 mb-1">${trimmedTable}</span>
          `);
        }
      });
    }
  }

  // Function to get table color based on prefix
  function getTableColor(tableName) {
    const prefix = tableName.charAt(0).toUpperCase();
    const colorMap = {
      'A': 'primary',   // Blue
      'B': 'warning',   // Yellow
      'C': 'danger',    // Orange/Red
      'H': 'purple',   // Purple
    };
    return colorMap[prefix] || 'primary';
  }

  // Save edited booking
  $('#save-edit-booking').on('click', function() {
    const formData = new FormData($('#editBookingForm')[0]);

    // Get selected payment type
    const paymentType = $('input[name="edit_payment_type"]:checked').val();
    formData.append('payment_type', paymentType);

    // Get selected special request
    const specialRequest = $('input[name="edit_special_request"]:checked').val();
    formData.append('special_request', specialRequest);

    // Show loading
    Swal.fire({
      title: 'Updating Booking...',
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    // Submit the form
    $.ajax({
      url: 'api/update_booking.php',
      method: 'POST',
      data: formData,
      processData: false,
      contentType: false,
      dataType: 'json',
      success: function(response) {
        Swal.close();

        if (response.success) {
          Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: 'Booking updated successfully',
            timer: 2000,
            showConfirmButton: false
          });

          // Close modal and refresh the booking list
          $('#editBookingModal').modal('hide');

          // Refresh the current date's bookings
          const currentDate = document.getElementById('myDate').value;
          if (currentDate) {
            fetchBookingsByDate(currentDate);
          }
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Error!',
            text: response.message || 'Failed to update booking'
          });
        }
      },
      error: function() {
        Swal.close();
        Swal.fire({
          icon: 'error',
          title: 'Error!',
          text: 'An error occurred while updating the booking'
        });
      }
    });
  });


  // function popupChangeDate
  function popupChangeDate(bookingId) {
    // Check if the button is disabled (for Change or Cancel status)
    const changeDateButton = document.querySelector(`[onclick="popupChangeDate(${bookingId})"]`);
    if (changeDateButton && changeDateButton.disabled) {
      Swal.fire({
        icon: 'warning',
        title: 'Cannot Change Date',
        text: 'Cannot change date for bookings with Change or Cancel status',
        timer: 3000,
        showConfirmButton: false
      });
      return;
    }

    // Set the booking ID in the hidden input
    $('#changeUseDateBookingId').val(bookingId);

    // Find the booking row to get the data - try multiple selectors
    let bookingRow = null;

    // Try to find by data-id attribute
    const inputElement = document.querySelector(`input[data-id="${bookingId}"]`);
    if (inputElement) {
      bookingRow = inputElement.closest('tr');
    }

    // If not found, try to find by other means (look for the row containing the booking ID)
    if (!bookingRow) {
      const allRows = document.querySelectorAll('tbody tr');
      for (let row of allRows) {
        const cells = row.querySelectorAll('td');
        for (let cell of cells) {
          if (cell.textContent.includes(bookingId)) {
            bookingRow = row;
            break;
          }
        }
        if (bookingRow) break;
      }
    }

    let currentUseDate = '';
    let floor = '';
    let tables = '';
    let adults = '';
    let children = '';
    let infants = '';

    if (bookingRow) {
      // Try to get booking data from various possible sources
      const useDateElement = bookingRow.querySelector('.user-usedate');
      const floorElement = bookingRow.querySelector('.user-floor');
      const tableElement = bookingRow.querySelector('.user-table');
      const adultElement = bookingRow.querySelector('.user-adult');
      const childElement = bookingRow.querySelector('.user-child');
      const infantElement = bookingRow.querySelector('.user-infant');

      // Get data from attributes or text content
      if (useDateElement) {
        currentUseDate = useDateElement.getAttribute('data-usedate') || useDateElement.textContent.trim();
      }

      if (floorElement) {
        floor = floorElement.getAttribute('data-floor') || floorElement.textContent.trim();
      }

      if (tableElement) {
        tables = tableElement.getAttribute('data-table') || tableElement.textContent.trim();
      }

      if (adultElement) {
        adults = adultElement.getAttribute('data-adult') || adultElement.textContent.trim();
      }

      if (childElement) {
        children = childElement.getAttribute('data-child') || childElement.textContent.trim();
      }

      if (infantElement) {
        infants = infantElement.getAttribute('data-infant') || infantElement.textContent.trim();
      }

      // If still no data, try to extract from table cells by position
      if (!currentUseDate || !floor || !tables || !adults || !children || !infants) {
        const cells = bookingRow.querySelectorAll('td');
        if (cells.length >= 8) {
          // Assuming standard table structure - adjust indices as needed
          if (!currentUseDate) currentUseDate = cells[2]?.textContent.trim() || ''; // Date column
          if (!floor) floor = cells[3]?.textContent.trim() || ''; // Floor column
          if (!adults) adults = cells[4]?.textContent.trim() || '0'; // Adult column
          if (!children) children = cells[5]?.textContent.trim() || '0'; // Child column
          if (!infants) infants = cells[6]?.textContent.trim() || '0'; // Infant column
          if (!tables) tables = cells[7]?.textContent.trim() || ''; // Tables column
        }
      }
    }

    // Format the current use date to dd-mm-yyyy
    let formattedCurrentDate = '';
    if (currentUseDate) {
      // Try to parse different date formats
      let date = new Date(currentUseDate);

      // If direct parsing fails, try dd-mm-yyyy format
      if (isNaN(date.getTime()) && currentUseDate.includes('-')) {
        const parts = currentUseDate.split('-');
        if (parts.length === 3) {
          // Try dd-mm-yyyy format
          if (parts[0].length <= 2) {
            date = new Date(parts[2], parts[1] - 1, parts[0]);
          }
          // Try yyyy-mm-dd format
          else if (parts[0].length === 4) {
            date = new Date(parts[0], parts[1] - 1, parts[2]);
          }
        }
      }

      if (!isNaN(date.getTime())) {
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        formattedCurrentDate = `${day}-${month}-${year}`;
      } else {
        formattedCurrentDate = currentUseDate; // fallback to original if parsing fails
      }
    }

    // Populate the modal fields
    const currentUseDateField = document.getElementById('currentUseDate');
    const changeFloorField = document.getElementById('changeFloor');
    const changeAdultsField = document.getElementById('changeAdults');
    const changeChildrenField = document.getElementById('changeChildren');
    const changeInfantsField = document.getElementById('changeInfants');
    const tablesContainer = document.getElementById('changeCurrentTables');

    if (currentUseDateField) {
      currentUseDateField.value = formattedCurrentDate;
    }

    if (changeFloorField) {
      changeFloorField.value = floor || '';
    }

    if (changeAdultsField) {
      changeAdultsField.value = adults || '0';
    }

    if (changeChildrenField) {
      changeChildrenField.value = children || '0';
    }

    if (changeInfantsField) {
      changeInfantsField.value = infants || '0';
    }

    // Display current tables
    if (tablesContainer) {
      if (tables && tables.trim()) {
        // Parse and display tables as badges
        const tableArray = tables.split(',').map(t => t.trim()).filter(t => t);
        tablesContainer.innerHTML = '';

        tableArray.forEach(table => {
          const badge = document.createElement('span');
          badge.className = 'badge bg-primary me-1 mb-1';
          badge.textContent = table;
          badge.id = `c-${formattedCurrentDate}-${table}`; // Add an ID for each badge
          badge.style.cursor = 'pointer';
          badge.title = 'Click to remove from current tables';
          badge.dataset.table = table;
          badge.dataset.selected = 'true';

          // Add click event for toggling
          badge.addEventListener('click', function() {
            toggleCurrentTable(this);
          });

          tablesContainer.appendChild(badge);
        });
      } else {
        tablesContainer.innerHTML = '<span class="text-muted">No tables assigned</span>';
      }
    }

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('changeUseDateModal'));
    modal.show();

    // Update adults count based on current tables
    setTimeout(() => {
      updateAdultsCount();
    }, 100); // Small delay to ensure DOM is updated

    // Load available tables for the current floor when modal opens
    if (floor) {
      const newUseDateField = document.getElementById('newUseDate');
      const newUseDate = newUseDateField ? newUseDateField.value : '';
      if (newUseDate) {
        loadAvailableTables(floor, newUseDate, bookingId);
      }
    }
  }

  // Function to load available tables for selected floor and date
  function loadAvailableTables(floor, date, excludeBookingId = null) {
    const availableTablesContainer = document.getElementById('changeAvailableTables');

    if (!availableTablesContainer) {
      console.error('Available tables container not found');
      return;
    }

    if (!floor || !date) {
      availableTablesContainer.innerHTML = '<span class="text-muted">Please select floor and date</span>';
      return;
    }

    // Show loading
    availableTablesContainer.innerHTML = '<span class="text-muted">Loading available tables...</span>';

    // Convert date format from yyyy-mm-dd to dd-mm-yyyy for API
    let formattedDate = date;
    if (date.includes('-')) {
      const dateParts = date.split('-');
      if (dateParts.length === 3) {
        // Check if it's already in yyyy-mm-dd format
        if (dateParts[0].length === 4) {
          formattedDate = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;
        } else {
          // Assume it's already in dd-mm-yyyy format
          formattedDate = date;
        }
      }
    }

    // Build API URL with optional booking ID to exclude
    let apiUrl = `api/get_available_tables.php?floor=${floor}&date=${encodeURIComponent(formattedDate)}`;
    if (excludeBookingId) {
      apiUrl += `&exclude_booking_id=${excludeBookingId}`;
    }

      // function check current tables and available tables
  function checkTables(formattedDate) {
    const currentTables = document.getElementById('changeCurrentTables');
    const availableTables = document.getElementById('changeAvailableTables');

    if (!currentTables || !availableTables) {
      console.error('Current or available tables container not found');
      return;
    }

    const currentTableBadges = currentTables.querySelectorAll('.badge');
    const availableTableBadges = availableTables.querySelectorAll('.badge');

    currentTableBadges.forEach(badge => {
      const table = badge.textContent;
      const availableBadge = availableTables.querySelector(`#A-${formattedDate}-${table}`);
      if (availableBadge) {
        availableBadge.classList.remove('bg-success');
        availableBadge.classList.add('bg-warning');
        availableBadge.title = 'Current table';
        availableBadge.style.cursor = 'not-allowed';
        availableBadge.style.display = 'none';
        availableBadge.onclick = function() {
          alert('This table is already assigned to this booking.');
        };
      }
    });
  }

    // Fetch available tables
    fetch(apiUrl)
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        if (data.success && data.tables) {
          displayAvailableTables(data.tables, floor, formattedDate); // Pass formattedDate to displayAvailableTables
          checkTables(formattedDate); // Check current tables and available tables after loading available tables
        } else {
          availableTablesContainer.innerHTML = `<span class="text-muted">${data.message || 'No available tables found'}</span>`;
        }
      })
      .catch(error => {
        console.error('Error loading available tables:', error);
        availableTablesContainer.innerHTML = '<span class="text-danger">Error loading tables</span>';
      });
  }

  // Function to display available tables as badges
  function displayAvailableTables(tables, floor, formattedDate) {
    const availableTablesContainer = document.getElementById('changeAvailableTables');

    if (!availableTablesContainer) {
      console.error('Available tables container not found');
      return;
    }

    availableTablesContainer.innerHTML = '';

    if (!tables || tables.length === 0) {
      availableTablesContainer.innerHTML = '<span class="text-muted">No available tables</span>';
      return;
    }

    try {
      tables.forEach(table => {
        const badge = document.createElement('span');
        badge.className = 'badge bg-success me-1 mb-1';
        badge.id = `A-${formattedDate}-${table}`; // Add an ID for each badge
        badge.textContent = table;
        badge.style.cursor = 'pointer';
        badge.title = 'Click to select/deselect table';
        badge.dataset.table = table;
        badge.dataset.selected = 'false';

        // Add click event for toggling
        badge.addEventListener('click', function() {
          toggleAvailableTable(this);
        });

        availableTablesContainer.appendChild(badge);
      });

      // Add summary
      const summary = document.createElement('div');
      summary.className = 'mt-2 small text-muted';
      summary.textContent = `${tables.length} available tables on Floor ${floor}`;
      availableTablesContainer.appendChild(summary);
    } catch (error) {
      console.error('Error displaying available tables:', error);
      availableTablesContainer.innerHTML = '<span class="text-danger">Error displaying tables</span>';
    }
  }

  // Function to toggle available table selection
  function toggleAvailableTable(badge) {
    const isSelected = badge.dataset.selected === 'true';
    const table = badge.dataset.table;

    if (isSelected) {
      // Deselect - gray out
      badge.className = 'badge bg-secondary me-1 mb-1';
      badge.dataset.selected = 'false';
      badge.title = 'Click to select table';
    } else {
      // Select - make green and add to current tables
      badge.className = 'badge bg-success me-1 mb-1';
      badge.dataset.selected = 'true';
      badge.title = 'Click to deselect table';

      // Add to current tables
      addToCurrentTables(table);
    }

    // Update adults count after toggle
    updateAdultsCount();
  }

  // Function to toggle current table (remove from current tables)
  function toggleCurrentTable(badge) {
    const table = badge.dataset.table;

    // Remove the badge from current tables
    badge.remove();

    // If there's a corresponding available table, make it selectable again
    const availableBadge = document.querySelector(`#changeAvailableTables span[data-table="${table}"]`);
    if (availableBadge) {
      availableBadge.className = 'badge bg-success me-1 mb-1';
      availableBadge.dataset.selected = 'false';
      availableBadge.title = 'Click to select table';
    }

    // Update adults count after removal
    updateAdultsCount();
  }

  // Function to add table to current tables
  function addToCurrentTables(table) {
    const currentTablesContainer = document.getElementById('changeCurrentTables');
    if (!currentTablesContainer) return;

    // Check if table already exists in current tables
    const existingBadge = currentTablesContainer.querySelector(`span[data-table="${table}"]`);
    if (existingBadge) return; // Already exists

    // Create new badge for current tables
    const badge = document.createElement('span');
    badge.className = 'badge bg-primary me-1 mb-1';
    badge.textContent = table;
    badge.style.cursor = 'pointer';
    badge.title = 'Click to remove from current tables';
    badge.dataset.table = table;
    badge.dataset.selected = 'true';

    // Add click event for removal
    badge.addEventListener('click', function() {
      toggleCurrentTable(this);
    });

    // Find the right place to insert (before summary if it exists)
    const summary = currentTablesContainer.querySelector('.mt-2.small.text-muted');
    if (summary) {
      currentTablesContainer.insertBefore(badge, summary);
    } else {
      currentTablesContainer.appendChild(badge);
    }

    // Update adults count after adding
    updateAdultsCount();
  }

  // Function to update adults count and new tables field based on current tables
  function updateAdultsCount() {
    const currentTablesContainer = document.getElementById('changeCurrentTables');
    const adultsField = document.getElementById('changeAdults');
    const newTablesField = document.getElementById('changeNewTables');

    if (!currentTablesContainer || !adultsField) return;

    // Get all table badges (spans with data-table attribute)
    const tableBadges = currentTablesContainer.querySelectorAll('span[data-table]');
    const tableCount = tableBadges.length;

    // Calculate adults count (number of tables * 2)
    const adultsCount = tableCount * 2;

    // Update the adults field
    adultsField.value = adultsCount;

    // Update the new tables field with comma-separated table list
    if (newTablesField) {
      const tableList = Array.from(tableBadges).map(badge => badge.dataset.table).join(',');
      newTablesField.value = tableList;
    }
  }

  // Add event listeners for floor and date changes in change date modal
  document.addEventListener('DOMContentLoaded', function() {
    // Floor change event
    const floorSelect = document.getElementById('changeFloor');
    if (floorSelect) {
      floorSelect.addEventListener('change', function() {
        try {
          const selectedFloor = this.value;
          const dateInput = document.getElementById('newUseDate');
          const bookingIdInput = document.getElementById('changeUseDateBookingId');

          const selectedDate = dateInput ? dateInput.value : '';
          const bookingId = bookingIdInput ? bookingIdInput.value : '';

          if (selectedFloor && selectedDate) {
            loadAvailableTables(selectedFloor, selectedDate, bookingId);
          }
        } catch (error) {
          console.error('Error in floor change event:', error);
        }
      });
    }

    // Date change event
    const dateInput = document.getElementById('newUseDate');
    if (dateInput) {
      dateInput.addEventListener('change', function() {
        try {
          const selectedDate = this.value;
          const floorSelect = document.getElementById('changeFloor');
          const bookingIdInput = document.getElementById('changeUseDateBookingId');

          const selectedFloor = floorSelect ? floorSelect.value : '';
          const bookingId = bookingIdInput ? bookingIdInput.value : '';

          if (selectedFloor && selectedDate) {
            loadAvailableTables(selectedFloor, selectedDate, bookingId);
          }
        } catch (error) {
          console.error('Error in date change event:', error);
        }
      });
    }
  });

  // function changeUseDate
  function changeUseDate() {
    const bookingId = $('#changeUseDateBookingId').val();
    const newUseDate = $('#newUseDate').val();
    const adults = $('#changeAdults').val();
    const children = $('#changeChildren').val();
    const infants = $('#changeInfants').val();
    const newTables = $('#changeNewTables').val();

    // Show loading
    Swal.fire({
      title: 'Changing Use Date...',
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    // Send request
    $.ajax({
      url: 'api/change_use_date.php',
      method: 'POST',
      data: {
        booking_id: bookingId,
        new_use_date: newUseDate,
        adults: adults,
        children: children,
        infants: infants,
        new_tables: newTables
      },
      dataType: 'json',
      success: function(response) {
        Swal.close();

        if (response.success) {
          Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: 'Use date changed successfully',
            timer: 2000,
            showConfirmButton: false
          });

          // Close modal and refresh the booking list
          $('#changeUseDateModal').modal('hide');

          // Refresh the current date's bookings
          const currentDate = document.getElementById('myDate').value;
          if (currentDate) {
            fetchBookingsByDate(currentDate);
          }
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Error!',
            text: response.message || 'Failed to change use date'
          });
        }
      },
      error: function() {
        Swal.close();
        Swal.fire({
          icon: 'error',
          title: 'Error!',
          text: 'Failed to change use date'
        });
      }
    });
  }

</script>

<!-- Change Use Date -->
 <div class="modal fade" id="changeUseDateModal" tabindex="-1" aria-labelledby="changeUseDateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changeUseDateModalLabel">Change Use Date</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="changeUseDateForm">
                    <input type="hidden" id="changeUseDateBookingId" name="booking_id">
                    <input type="hidden" class="form-control" id="changeNewTables" name="new_tables">

                    <div class="row g-2">
                      <!-- Current Use Date -->
                      <div class="col-md-6 mb-3">
                          <label for="currentUseDate" class="form-label">Current Use Date</label>
                          <input type="text" class="form-control" id="currentUseDate" name="current_use_date" disabled readonly>
                      </div>

                      <!-- New Use Date -->
                      <div class="col-md-6 mb-3">
                          <label for="newUseDate" class="form-label">New Use Date</label>
                          <input type="date" class="form-control" id="newUseDate" name="new_use_date" required>
                      </div>
                    </div>

                    <div class="row g-2">
                      <!-- Floor -->
                      <div class="col-md-6 mb-3">
                          <label for="changeFloor" class="form-label">Floor</label>
                          <select class="form-select" id="changeFloor" name="floor" required>
                              <option value="">Select Floor</option>
                              <option value="1">Floor 1</option>
                              <option value="2">Floor 2</option>
                              <option value="3">Floor 3</option>
                          </select>
                      </div>

                      <!-- Adults, Children, Infants -->
                      <div class="col-md-2">
                        <div class="mb-3">
                            <label for="changeAdults" class="form-label">Adults</label>
                            <input type="number" class="form-control" id="changeAdults" name="adults" min="0" value="0">
                        </div>
                      </div>
                      <div class="col-md-2">
                        <div class="mb-3">
                            <label for="changeChildren" class="form-label">Children</label>
                            <input type="number" class="form-control" id="changeChildren" name="children" min="0" value="0">
                        </div>
                      </div>
                      <div class="col-md-2">
                        <div class="mb-3">
                            <label for="changeInfants" class="form-label">Infants</label>
                            <input type="number" class="form-control" id="changeInfants" name="infants" min="0" value="0">
                        </div>
                     </div>
                    </div>

                    <!-- Current Tables -->
                    <div class="mb-3">
                        <label class="form-label">Current Tables</label>
                        <div class="border rounded p-2 bg-light">
                            <div id="changeCurrentTables" class="d-flex flex-wrap gap-1">
                                <!-- Current tables badges will be displayed here -->
                            </div>
                        </div>
                    </div>

                    <!-- Available Tables -->
                    <div class="mb-3">
                        <label class="form-label">Available Tables</label>
                        <div class="border rounded p-2 bg-light">
                            <div id="changeAvailableTables" class="d-flex flex-wrap gap-1">
                                <!-- Available tables badges will be displayed here -->
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="changeUseDate()">Change Date</button>
            </div>
            </div>
        </div>
    </div>
</div>




<!-- Booking Status Modal -->
 <div class="modal fade" id="bookingStatusModal" tabindex="-1" aria-labelledby="bookingStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bookingStatusModalLabel">Update Booking Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="bookingStatusForm">
                    <input type="hidden" id="bookingStatusId" name="booking_id">
                    <div class="mb-3">
                        <label for="bookingStatus" class="form-label">Booking Status</label>
                        <select class="form-select" id="bookingStatus" name="booking_status" required>
                            <option value="Pending">Pending</option>
                            <option value="Complete">Complete</option>
                            <option value="Cancel">Cancel</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="bookingNoteChange" class="form-label">Note (Optional)</label>
                        <textarea class="form-control" id="bookingNoteChange" name="booking_note" rows="2" placeholder="Add a note about this booking status change..." required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="updateBookingStatus()">Update Status</button>
            </div>
        </div>
    </div>
</div>




<!-- Payment Status Modal -->
<div class="modal fade" id="paymentStatusModal" tabindex="-1" aria-labelledby="paymentStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="paymentStatusModalLabel">Update Payment Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="paymentStatusForm">
                    <input type="hidden" id="paymentBookingId" name="booking_id">
                    <div class="mb-3">
                        <label for="paymentStatus" class="form-label">Payment Status</label>
                        <select class="form-select" id="paymentStatus" name="payment_status" required>
                            <option value="WP">WP (Waiting Payment)</option>
                            <option value="Paid">Paid</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="paymentNote" class="form-label">Note (Optional)</label>
                        <textarea class="form-control" id="paymentNote" name="note" rows="2" placeholder="Add a note about this payment status change..."></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="paymentReference" class="form-label">Payment Reference Images (Optional)</label>
                        <input type="file" class="form-control" id="paymentReference" name="payment_reference[]" accept="image/*" multiple>
                        <div class="form-text">Upload payment slips, receipts, or reference images (JPG, PNG, GIF - Max 5MB each, up to 5 images)</div>

                        <!-- Existing attachments display -->
                        <div id="existingAttachments" class="mt-2" style="display: none;">
                            <div class="alert alert-info">
                                <strong>Current Attachments:</strong>
                                <div id="existingImagesContainer" class="mt-2 row">
                                    <!-- Existing images will be loaded here -->
                                </div>
                            </div>
                        </div>

                        <!-- New images preview -->
                        <div id="imagePreview" class="mt-2" style="display: none;">
                            <div class="alert alert-success">
                                <strong>New Images Preview:</strong>
                                <div id="previewImagesContainer" class="mt-2 row">
                                    <!-- New image previews will be shown here -->
                                </div>
                                <div class="mt-2">
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeAllImages()">Remove All New Images</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="updatePaymentStatus()">Update Status</button>
            </div>
        </div>
    </div>
</div>



<!-- Bootstrap Datepicker JS -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
